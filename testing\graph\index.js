const canvas = document.getElementById('windSpeedChart');
const ctx = canvas.getContext('2d');

// Data for the curve
const dataPoints = [10, 35, 25, 60, 50, 70, 40, 60, 20, 30];
const width = canvas.width;
const height = canvas.height;
const stepX = width / (dataPoints.length - 1);

// Create a gradient for the curvy line
const lineGradient = ctx.createLinearGradient(0, 0, width, 0);
lineGradient.addColorStop(0, '#4a90e2');  // Start color (blue)
lineGradient.addColorStop(1, '#50e3c2');  // End color (light teal)

// Draw a smooth curvy line
ctx.beginPath();
ctx.moveTo(0, height - dataPoints[0]);

for (let i = 1; i < dataPoints.length; i++) {
  const x = i * stepX;
  const y = height - dataPoints[i];
  
  // Use cubic Bézier curves for a smoother transition
  const cpX = (x + (i - 1) * stepX) / 2; // Control point for smoothness
  const cpY = height - ((dataPoints[i - 1] + dataPoints[i]) / 2); // Average height

  ctx.bezierCurveTo(cpX, cpY, cpX, y, x, y); // Cubic curve
}

ctx.strokeStyle = lineGradient;
ctx.lineWidth = 2;
ctx.stroke();

// Draw vertical bars with gradient
const verticalLineOffset = 5; // Adjust this value to control how low the vertical lines are drawn
dataPoints.forEach((dataPoint, i) => {
  const x = i * stepX;
  
  // Set the vertical line height to be visibly below the corresponding data point
  const barHeight = height - (dataPoint * 0.46); // Decreased to ensure it's less than the data point

  ctx.beginPath();
  ctx.moveTo(x, height);
  ctx.lineTo(x, barHeight);
  
  // Create a gradient for each vertical line with shades of blue
  const barGradient = ctx.createLinearGradient(x, barHeight, x, height);
  barGradient.addColorStop(0, 'rgba(74, 144, 226, 1)'); // Solid blue
  barGradient.addColorStop(1, 'rgba(255, 255, 255, 0.3)'); // Fading to white

  ctx.strokeStyle = barGradient; // Apply gradient to the vertical line
  ctx.lineWidth = 5; // Thickness of the vertical lines
  ctx.stroke();
});
