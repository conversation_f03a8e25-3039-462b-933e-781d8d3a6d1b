import { useState, useEffect } from 'react';
import { motion } from 'framer-motion';
import { Doughnut, Line } from 'react-chartjs-2';
import {
  Chart as ChartJS,
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler
} from 'chart.js';
import {
  Card,
  HighlightsContainer,
  HighlightCard,
  HighlightTitle,
  HighlightValue
} from '../styles/StyledComponents';

// Register ChartJS components
ChartJS.register(
  ArcElement,
  Tooltip,
  Legend,
  CategoryScale,
  LinearScale,
  PointElement,
  LineElement,
  Filler
);

const WeatherHighlights = ({ data }) => {
  const [windData, setWindData] = useState([]);

  useEffect(() => {
    if (data) {
      // Generate fake wind data for the chart
      const baseWindSpeed = data.wind.speed;
      const newWindData = Array(24).fill().map((_, i) => {
        const hour = i;
        const fluctuation = Math.random() * 5 - 2.5; // Random fluctuation between -2.5 and 2.5
        return {
          hour: `${hour}:00`,
          speed: Math.max(0, baseWindSpeed + fluctuation)
        };
      });
      setWindData(newWindData);
    }
  }, [data]);

  if (!data) return null;

  const { main, wind } = data;

  // Humidity donut chart config
  const humidityChartData = {
    datasets: [{
      data: [main.humidity, 100 - main.humidity],
      backgroundColor: [
        'rgba(94, 130, 244, 0.8)',
        'rgba(94, 130, 244, 0.1)'
      ],
      borderWidth: 0,
      cutout: '80%',
      borderRadius: 10
    }]
  };

  // Wind speed line chart config
  const windChartData = {
    labels: windData.map(item => item.hour),
    datasets: [{
      label: 'Wind Speed (km/h)',
      data: windData.map(item => item.speed),
      borderColor: 'rgba(94, 130, 244, 1)',
      borderWidth: 2,
      backgroundColor: (context) => {
        const ctx = context.chart.ctx;
        const gradient = ctx.createLinearGradient(0, 0, 0, 300);
        gradient.addColorStop(0, 'rgba(94, 130, 244, 0.5)');
        gradient.addColorStop(1, 'rgba(94, 130, 244, 0)');
        return gradient;
      },
      pointBackgroundColor: 'rgba(51, 221, 251, 1)',
      pointBorderColor: 'rgba(255, 255, 255, 1)',
      pointBorderWidth: 2,
      pointRadius: 4,
      pointHoverRadius: 6,
      tension: 0.4,
      fill: true
    }]
  };

  const windChartOptions = {
    responsive: true,
    plugins: {
      legend: {
        display: false
      },
      tooltip: {
        mode: 'index',
        intersect: false,
        backgroundColor: 'rgba(255, 255, 255, 0.95)',
        titleColor: '#2C2C54',
        bodyColor: '#2C2C54',
        borderColor: 'rgba(94, 130, 244, 0.3)',
        borderWidth: 1,
        padding: 12,
        cornerRadius: 8,
        titleFont: {
          size: 14,
          weight: 'bold'
        },
        bodyFont: {
          size: 13
        },
        displayColors: false,
        callbacks: {
          // Use media query to check for dark mode
          labelTextColor: function() {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? '#FFFFFF' : '#2C2C54';
          },
          titleTextColor: function() {
            return window.matchMedia('(prefers-color-scheme: dark)').matches ? '#FFFFFF' : '#2C2C54';
          },
          // Override the background color for dark mode
          beforeTooltipDraw: function(context) {
            if (window.matchMedia('(prefers-color-scheme: dark)').matches) {
              context.tooltip.options.backgroundColor = 'rgba(37, 37, 71, 0.95)';
            }
          }
        }
      }
    },
    scales: {
      y: {
        beginAtZero: true,
        grid: {
          color: 'rgba(94, 130, 244, 0.1)',
          drawBorder: false
        },
        ticks: {
          color: '#6F6F9A',
          font: {
            size: 11
          },
          padding: 8
        },
        border: {
          dash: [4, 4]
        }
      },
      x: {
        grid: {
          display: false
        },
        ticks: {
          color: '#6F6F9A',
          maxRotation: 0,
          autoSkip: true,
          maxTicksLimit: 6,
          font: {
            size: 11
          },
          padding: 8
        }
      }
    },
    elements: {
      line: {
        tension: 0.4
      },
      point: {
        radius: 4,
        hoverRadius: 6
      }
    },
    interaction: {
      mode: 'nearest',
      intersect: false
    },
    animation: {
      duration: 1000,
      easing: 'easeOutQuart'
    }
  };

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.2 }}
    >
      <Card>
        <h2>Today's Highlights</h2>
        <HighlightsContainer>
          {/* Humidity Card */}
          <HighlightCard
            as={motion.div}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <HighlightTitle>
              <span className="material-symbols-rounded">humidity_percentage</span>
              Humidity
            </HighlightTitle>
            <div style={{ position: 'relative', width: '100%', height: '180px' }}>
              <Doughnut data={humidityChartData} options={{
                responsive: true,
                maintainAspectRatio: false,
                plugins: {
                  legend: { display: false },
                  tooltip: { enabled: false }
                },
                cutout: '80%',
                rotation: -90,
                circumference: 180,
                animation: {
                  animateRotate: true,
                  animateScale: true,
                  duration: 2000,
                  easing: 'easeOutQuart'
                }
              }} />
              <div style={{
                position: 'absolute',
                top: '50%',
                left: '50%',
                transform: 'translate(-50%, -20%)',
                fontSize: '2rem',
                fontWeight: 'bold',
                color: 'var(--primary-color)',
                display: 'flex',
                flexDirection: 'column',
                alignItems: 'center'
              }}>
                {main.humidity}%
                <span style={{
                  fontSize: '0.8rem',
                  fontWeight: 'normal',
                  color: 'var(--text-secondary)',
                  marginTop: '5px'
                }}>
                  Humidity
                </span>
              </div>
            </div>
          </HighlightCard>

          {/* Feels Like Card */}
          <HighlightCard
            as={motion.div}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <HighlightTitle>
              <span className="material-symbols-rounded">thermostat</span>
              Feels Like
            </HighlightTitle>
            <HighlightValue>{Math.round(main.feels_like)}°C</HighlightValue>
            <div style={{ color: 'var(--text-secondary)', fontSize: '0.9rem' }}>
              Temperature feels like
            </div>
          </HighlightCard>

          {/* Pressure Card */}
          <HighlightCard
            as={motion.div}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <HighlightTitle>
              <span className="material-symbols-rounded">speed</span>
              Pressure
            </HighlightTitle>
            <HighlightValue>{main.pressure} hPa</HighlightValue>
            <div style={{ color: 'var(--text-secondary)', fontSize: '0.9rem' }}>
              Atmospheric pressure
            </div>
          </HighlightCard>

          {/* Wind Speed Card */}
          <HighlightCard
            as={motion.div}
            whileHover={{ scale: 1.02 }}
            transition={{ duration: 0.2 }}
          >
            <HighlightTitle>
              <span className="material-symbols-rounded">air</span>
              Wind Speed
            </HighlightTitle>
            <HighlightValue>{wind.speed} km/h</HighlightValue>
            <div style={{ color: 'var(--text-secondary)', fontSize: '0.9rem' }}>
              Wind direction: {wind.deg}°
            </div>
          </HighlightCard>
        </HighlightsContainer>

        {/* Wind Speed Chart */}
        <div style={{ marginTop: '30px' }}>
          <h3 style={{
            marginBottom: '15px',
            fontSize: '1.3rem',
            fontWeight: '600',
            color: 'var(--text-primary)',
            display: 'flex',
            alignItems: 'center',
            gap: '8px'
          }}>
            <span className="material-symbols-rounded" style={{ color: 'var(--primary-color)' }}>
              monitoring
            </span>
            Wind Speed (24h)
          </h3>
          <div style={{
            height: '250px',
            background: 'var(--background-secondary)',
            borderRadius: 'var(--border-radius-lg)',
            padding: '20px',
            boxShadow: 'var(--shadow-sm)'
          }}>
            <Line data={windChartData} options={windChartOptions} />
          </div>
        </div>
      </Card>
    </motion.div>
  );
};

export default WeatherHighlights;
