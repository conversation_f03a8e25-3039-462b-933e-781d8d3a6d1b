import { useState } from 'react';
import useWeather from '../hooks/useWeather';
import SearchBar from '../components/SearchBar';
import CurrentWeather from '../components/CurrentWeather';
import WeatherHighlights from '../components/WeatherHighlights';
import Forecast from '../components/Forecast';
import WeatherMap from '../components/WeatherMap';
import Loading from '../components/Loading';
import { WeatherBackground } from '../components/weather-effects';
import {
  <PERSON>pp<PERSON><PERSON><PERSON>,
  PageContainer,
  ErrorContainer,
  Header,
  Logo,
  Button,
  WeatherGrid,
  ThemeToggle
} from '../styles/StyledComponents';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const BackgroundGradient = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: var(--background-primary);
  z-index: -1;
`;

const ChangeLocationButton = styled(Button)`
  display: flex;
  align-items: center;
  gap: 8px;
`;

const WeatherPage = () => {
  const [showSearch, setShowSearch] = useState(true);
  const { currentWeather, forecast, loading, error, searchCity, city } = useWeather();

  const handleSearch = (city) => {
    searchCity(city);
    setShowSearch(false);
  };

  const handleChangeCity = () => {
    setShowSearch(true);
  };

  // Determine weather condition and intensity for the background effect
  const getWeatherCondition = () => {
    if (!currentWeather || !currentWeather.weather || !currentWeather.weather[0]) {
      return { condition: 'clear', intensity: 'medium' };
    }

    const weatherData = currentWeather.weather[0];
    const condition = weatherData.main || weatherData.description;

    // Determine intensity based on weather parameters
    let intensity = 'medium';

    // Check temperature for extreme heat (for sunny/clear conditions)
    if ((condition.toLowerCase().includes('clear') || condition.toLowerCase().includes('sun')) &&
        currentWeather.main && currentWeather.main.temp > 30) {
      intensity = 'heavy'; // Heatwave
    }
    // Check for extreme cold
    else if (currentWeather.main && currentWeather.main.temp < -10) {
      intensity = 'heavy'; // Extreme cold
    }
    // Rain intensity
    else if (condition.toLowerCase().includes('rain') || condition.toLowerCase().includes('drizzle')) {
      // Check rain intensity based on precipitation or description
      if (weatherData.description.includes('heavy') ||
          (currentWeather.rain && currentWeather.rain['1h'] > 7)) {
        intensity = 'heavy';
      } else if (weatherData.description.includes('light') ||
                (currentWeather.rain && currentWeather.rain['1h'] < 2)) {
        intensity = 'light';
      }
    }
    // Snow intensity
    else if (condition.toLowerCase().includes('snow')) {
      // Check snow intensity
      if (weatherData.description.includes('heavy')) {
        intensity = 'heavy';
      } else if (weatherData.description.includes('light')) {
        intensity = 'light';
      }
    }
    // Cloud intensity
    else if (condition.toLowerCase().includes('cloud')) {
      // Check cloud intensity based on cloudiness percentage
      if (currentWeather.clouds && currentWeather.clouds.all > 75) {
        intensity = 'heavy';
      } else if (currentWeather.clouds && currentWeather.clouds.all < 30) {
        intensity = 'light';
      }
    }
    // Fog/mist intensity
    else if (condition.toLowerCase().includes('fog') || condition.toLowerCase().includes('mist')) {
      if (weatherData.description.includes('dense') || weatherData.description.includes('thick')) {
        intensity = 'heavy';
      } else if (weatherData.description.includes('light') || weatherData.description.includes('thin')) {
        intensity = 'light';
      }
    }

    // Log the weather condition for debugging
    console.log('Weather condition:', condition, 'Intensity:', intensity);

    return { condition, intensity };
  };

  const { condition, intensity } = getWeatherCondition();

  return (
    <>
      {currentWeather && (
        <WeatherBackground
          weatherCondition={condition}
          intensity={intensity}
        />
      )}
      {!currentWeather && <BackgroundGradient />}
      <AppContainer>
        <Header>
          <Logo>
            <span className="material-symbols-rounded">partly_cloudy_day</span>
            Weather Forecast
          </Logo>
          {!showSearch && currentWeather && (
            <ChangeLocationButton
              primary
              as={motion.button}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              onClick={handleChangeCity}
            >
              <span className="material-symbols-rounded">location_on</span>
              Change Location
            </ChangeLocationButton>
          )}
        </Header>

        <PageContainer>
          {error && (
            <ErrorContainer>
              <p>{error}</p>
              <Button primary onClick={() => setShowSearch(true)}>Try Again</Button>
            </ErrorContainer>
          )}

          {showSearch && (
            <motion.div
              initial={{ opacity: 0, y: -20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.5 }}
            >
              <SearchBar onSearch={handleSearch} initialValue={city} />
            </motion.div>
          )}

          {loading ? (
            <Loading />
          ) : (
            !showSearch && currentWeather && (
              <motion.div
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.5 }}
              >
                <WeatherGrid>
                  <CurrentWeather data={currentWeather} />
                  <div style={{ display: 'flex', flexDirection: 'column', gap: 'var(--spacing-xl)' }}>
                    <WeatherHighlights data={currentWeather} />
                    <Forecast data={forecast} />
                  </div>
                </WeatherGrid>
                <div style={{ marginTop: 'var(--spacing-xl)' }}>
                  <WeatherMap data={currentWeather} />
                </div>
              </motion.div>
            )
          )}
        </PageContainer>
      </AppContainer>
    </>
  );
};

export default WeatherPage;
