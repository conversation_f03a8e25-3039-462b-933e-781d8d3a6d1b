import { BrowserRouter as Router, Routes, Route, Navigate } from 'react-router-dom';
import { useState, useEffect } from 'react';
import WeatherPage from './pages/WeatherPage';
import OnboardingPage from './pages/OnboardingPage';
import GlobalStyles from './styles/GlobalStyles';

function App() {
  const [firstVisit, setFirstVisit] = useState(true);

  useEffect(() => {
    // Check if user has visited before
    const hasVisited = localStorage.getItem('hasVisitedWeatherApp');
    if (hasVisited) {
      setFirstVisit(false);
    } else {
      // Set flag for future visits
      localStorage.setItem('hasVisitedWeatherApp', 'true');
    }
  }, []);

  return (
    <>
      <GlobalStyles />
      <Router>
        <Routes>
          <Route path="/" element={firstVisit ? <OnboardingPage /> : <Navigate to="/weather" />} />
          <Route path="/onboarding" element={<OnboardingPage />} />
          <Route path="/weather" element={<WeatherPage />} />
          <Route path="*" element={<Navigate to="/" />} />
        </Routes>
      </Router>
    </>
  );
}

export default App;
