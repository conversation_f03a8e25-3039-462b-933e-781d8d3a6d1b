import styled from 'styled-components';
import { motion } from 'framer-motion';

// Container components
export const AppContainer = styled.div`
  max-width: 1200px;
  margin: 0 auto;
  padding: var(--spacing-md);
  width: 100%;
`;

export const PageContainer = styled.main`
  display: flex;
  flex-direction: column;
  flex: 1;
  width: 100%;
  gap: var(--spacing-xl);
`;

export const WeatherGrid = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-xl);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 1fr;
  }
`;

// Card components
export const Card = styled(motion.div)`
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-card);
  margin-bottom: var(--spacing-lg);
  overflow: hidden;
  position: relative;
  transition: transform var(--transition-normal), box-shadow var(--transition-normal);

  &:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
  }

  h2 {
    font-size: 1.5rem;
    font-weight: 700;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
  }

  @media (prefers-color-scheme: dark) {
    background: var(--background-card-dark);

    h2 {
      color: var(--text-light);
    }
  }
`;

export const GlassCard = styled(Card)`
  background: var(--background-glass);
  backdrop-filter: blur(10px);
  border: 1px solid rgba(255, 255, 255, 0.1);

  @media (prefers-color-scheme: dark) {
    background: var(--background-glass-dark);
  }
`;

// Weather components
export const WeatherContainer = styled.div`
  display: grid;
  grid-template-columns: 1fr;
  gap: var(--spacing-lg);

  @media (min-width: 768px) {
    grid-template-columns: 1fr 1fr;
  }

  @media (min-width: 1024px) {
    grid-template-columns: 1fr 2fr;
  }
`;

export const CurrentWeatherCard = styled(Card)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  text-align: center;
  padding: var(--spacing-xl);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 150px;
    background: var(--gradient-primary);
    border-radius: var(--border-radius-lg) var(--border-radius-lg) 0 0;
    z-index: 0;
  }

  @media (min-width: 768px) {
    grid-row: span 2;
  }
`;

export const LocationBadge = styled.div`
  background: rgba(255, 255, 255, 0.2);
  color: white;
  padding: 8px 16px;
  border-radius: var(--border-radius-md);
  font-weight: 600;
  margin-bottom: var(--spacing-md);
  box-shadow: var(--shadow-sm);
  backdrop-filter: blur(4px);
  display: flex;
  align-items: center;
  gap: 8px;
  z-index: 1;

  svg {
    width: 16px;
    height: 16px;
  }
`;

export const WeatherIcon = styled.img`
  width: 140px;
  height: 140px;
  margin: var(--spacing-md) 0;
  filter: drop-shadow(0 8px 16px rgba(0, 0, 0, 0.2));
  z-index: 1;
`;

export const Temperature = styled.div`
  font-size: 5rem;
  font-weight: 800;
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);
  line-height: 1;
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

export const WeatherDescription = styled.div`
  font-size: 1.4rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  text-transform: capitalize;
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

export const DateTime = styled.div`
  font-size: 1rem;
  margin-bottom: var(--spacing-lg);
  color: var(--text-secondary);
  background: rgba(255, 255, 255, 0.1);
  padding: 8px 16px;
  border-radius: 20px;
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

export const WeatherDetails = styled.div`
  display: flex;
  justify-content: space-around;
  width: 100%;
  margin-top: var(--spacing-lg);
  background: rgba(94, 130, 244, 0.05);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  z-index: 1;
`;

export const WeatherDetail = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;

  span:first-child {
    font-size: 0.9rem;
    color: var(--text-secondary);
    margin-bottom: var(--spacing-xs);
    display: flex;
    align-items: center;
    gap: 4px;

    svg {
      width: 16px;
      height: 16px;
    }

    @media (prefers-color-scheme: dark) {
      color: var(--text-light-secondary);
    }
  }

  span:last-child {
    font-size: 1.2rem;
    font-weight: 600;
    color: var(--text-primary);

    @media (prefers-color-scheme: dark) {
      color: var(--text-light);
    }
  }
`;

export const HighlightsContainer = styled.div`
  display: grid;
  grid-template-columns: repeat(auto-fit, minmax(220px, 1fr));
  gap: var(--spacing-lg);
`;

export const HighlightCard = styled(Card)`
  padding: var(--spacing-lg);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform var(--transition-fast);

  &:hover {
    transform: translateY(-5px);
  }
`;

export const HighlightTitle = styled.div`
  font-size: 1.1rem;
  font-weight: 600;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-sm);
  display: flex;
  align-items: center;
  gap: 8px;

  svg {
    width: 20px;
    height: 20px;
    color: var(--primary-color);
  }

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

export const HighlightValue = styled.div`
  font-size: 2rem;
  font-weight: 700;
  margin: var(--spacing-sm) 0;
  color: var(--text-primary);

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

// Form components
export const SearchContainer = styled.div`
  display: flex;
  width: 100%;
  max-width: 600px;
  margin: 0 auto var(--spacing-lg);
  position: relative;

  @media (max-width: 480px) {
    flex-direction: column;
  }
`;

export const SearchInput = styled.input`
  padding: 16px 24px;
  padding-left: 50px;
  border-radius: var(--border-radius-md);
  border: 2px solid rgba(94, 130, 244, 0.1);
  background: var(--background-secondary);
  color: var(--text-primary);
  flex: 1;
  margin-right: var(--spacing-sm);
  font-size: 1rem;
  font-weight: 500;
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);

  &:focus {
    outline: none;
    border-color: var(--primary-color);
    box-shadow: 0 0 0 3px rgba(94, 130, 244, 0.2);
  }

  &::placeholder {
    color: var(--text-secondary);
    opacity: 0.7;
  }

  @media (prefers-color-scheme: dark) {
    background: var(--background-card-dark);
    color: var(--text-light);
    border-color: rgba(255, 255, 255, 0.1);

    &::placeholder {
      color: var(--text-light-secondary);
    }
  }

  @media (max-width: 480px) {
    margin-right: 0;
    margin-bottom: var(--spacing-sm);
  }
`;

export const SearchIcon = styled.div`
  position: absolute;
  left: 16px;
  top: 50%;
  transform: translateY(-50%);
  color: var(--primary-color);

  svg {
    width: 20px;
    height: 20px;
  }

  @media (max-width: 480px) {
    top: 24px;
  }
`;

export const Button = styled.button`
  background: ${props => props.primary ? 'var(--gradient-primary)' : 'var(--background-secondary)'};
  color: ${props => props.primary ? 'white' : 'var(--primary-color)'};
  padding: ${props => props.small ? '8px 16px' : '12px 24px'};
  border-radius: var(--border-radius-md);
  font-weight: 600;
  font-size: ${props => props.small ? '0.9rem' : '1rem'};
  transition: all var(--transition-fast);
  box-shadow: ${props => props.primary ? 'var(--shadow-md)' : 'var(--shadow-sm)'};
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;

  &:hover {
    transform: translateY(-2px);
    box-shadow: ${props => props.primary ? 'var(--shadow-lg)' : 'var(--shadow-md)'};
    background: ${props => props.primary ? 'linear-gradient(135deg, #4A73F8, #6A8FFF)' : 'rgba(94, 130, 244, 0.1)'};
  }

  &:active {
    transform: translateY(0);
  }

  svg {
    width: 20px;
    height: 20px;
  }

  @media (prefers-color-scheme: dark) {
    background: ${props => props.primary ? 'var(--gradient-primary)' : 'var(--background-card-dark)'};
    color: ${props => props.primary ? 'white' : 'var(--primary-light)'};

    &:hover {
      background: ${props => props.primary ? 'linear-gradient(135deg, #4A73F8, #6A8FFF)' : 'rgba(94, 130, 244, 0.2)'};
    }
  }
`;

// Forecast components
export const ForecastHeader = styled.h2`
  font-size: 1.8rem;
  margin-bottom: var(--spacing-md);
  color: var(--text-primary);
  font-weight: 700;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

export const ForecastDescription = styled.p`
  color: var(--text-secondary);
  margin-bottom: var(--spacing-md);
  font-size: 1rem;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

export const ForecastContainer = styled.div`
  display: flex;
  gap: var(--spacing-md);
  overflow-x: auto;
  padding: var(--spacing-sm) 0 var(--spacing-md) 0;

  &::-webkit-scrollbar {
    height: 6px;
  }

  /* Add padding to allow shadow to be visible */
  padding-top: 10px;
  padding-bottom: 20px;
  margin: -10px -10px -20px -10px;
  padding-left: 10px;
  padding-right: 10px;
`;

export const ForecastCard = styled.div`
  flex: 0 0 auto;
  width: 130px;
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  padding: var(--spacing-md);
  display: flex;
  flex-direction: column;
  align-items: center;
  transition: transform var(--transition-fast), box-shadow var(--transition-fast);
  box-shadow: var(--shadow-sm);

  &:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-md);
  }

  @media (prefers-color-scheme: dark) {
    background: var(--background-card-dark);
  }
`;

export const ForecastCardContent = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  width: 100%;
`;

export const ForecastDay = styled.div`
  font-weight: 600;
  margin-bottom: var(--spacing-sm);
  color: var(--text-primary);

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

export const ForecastIcon = styled.img`
  width: 60px;
  height: 60px;
  margin: var(--spacing-sm) 0;
`;

export const ForecastTemp = styled.div`
  font-size: 1.5rem;
  font-weight: 700;
  color: var(--primary-color);

  @media (prefers-color-scheme: dark) {
    color: var(--primary-light);
  }
`;

// Map component
export const MapContainer = styled.div`
  height: 350px;
  width: 100%;
  border-radius: var(--border-radius-lg);
  overflow: hidden;
  margin-top: var(--spacing-md);
  box-shadow: var(--shadow-card);
  border: 1px solid rgba(94, 130, 244, 0.1);
`;

// Loading and error components
export const LoadingContainer = styled.div`
  display: flex;
  justify-content: center;
  align-items: center;
  min-height: 300px;
  flex-direction: column;
  gap: var(--spacing-md);

  svg {
    color: var(--primary-color);
    width: 40px;
    height: 40px;
  }
`;

export const LoadingText = styled.div`
  color: var(--text-secondary);
  font-weight: 500;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

export const ErrorContainer = styled(Card)`
  text-align: center;
  max-width: 500px;
  margin: 0 auto;
  border-left: 4px solid var(--danger-color);

  p {
    margin-bottom: var(--spacing-md);
    color: var(--danger-color);
    font-weight: 500;
  }

  button {
    background: var(--gradient-primary);
    color: white;
    padding: var(--spacing-sm) var(--spacing-lg);
    border-radius: var(--border-radius-md);
    font-weight: 600;
    transition: all var(--transition-fast);

    &:hover {
      transform: translateY(-2px);
      box-shadow: var(--shadow-md);
    }
  }
`;

// Header components
export const Header = styled.header`
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-md) 0;
  margin-bottom: var(--spacing-lg);

  @media (max-width: 768px) {
    flex-direction: column;
    align-items: flex-start;
    gap: var(--spacing-md);
  }
`;

export const Logo = styled.div`
  font-size: 1.8rem;
  font-weight: 800;
  color: var(--primary-color);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);

  svg {
    width: 32px;
    height: 32px;
  }

  @media (prefers-color-scheme: dark) {
    color: var(--primary-light);
  }

  @media (max-width: 768px) {
    margin-bottom: var(--spacing-sm);
  }
`;

// Theme toggle
export const ThemeToggle = styled.button`
  width: 48px;
  height: 48px;
  border-radius: var(--border-radius-circle);
  display: flex;
  align-items: center;
  justify-content: center;
  background: var(--background-secondary);
  color: var(--text-primary);
  box-shadow: var(--shadow-sm);
  transition: all var(--transition-fast);

  svg {
    width: 24px;
    height: 24px;
  }

  &:hover {
    background: rgba(94, 130, 244, 0.1);
    transform: translateY(-2px);
  }

  @media (prefers-color-scheme: dark) {
    background: var(--background-card-dark);
    color: var(--text-light);

    &:hover {
      background: rgba(255, 255, 255, 0.1);
    }
  }
`;
