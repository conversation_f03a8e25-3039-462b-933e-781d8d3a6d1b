import React from 'react';
import styled, { keyframes } from 'styled-components';
import { motion } from 'framer-motion';

// Define animations
const fadeIn = keyframes`
  from { opacity: 0; }
  to { opacity: 1; }
`;

const float = keyframes`
  0% { transform: translateY(0px); }
  50% { transform: translateY(-10px); }
  100% { transform: translateY(0px); }
`;

const drift = keyframes`
  0% { transform: translateX(0); }
  100% { transform: translateX(20px); }
`;

const pulse = keyframes`
  0% { opacity: 0.6; }
  50% { opacity: 0.8; }
  100% { opacity: 0.6; }
`;

const heatDistortion = keyframes`
  0% { transform: scale(1) translateY(0); }
  25% { transform: scale(1.02, 0.98) translateY(5px); }
  50% { transform: scale(0.98, 1.02) translateY(-5px); }
  75% { transform: scale(1.02, 0.98) translateY(5px); }
  100% { transform: scale(1) translateY(0); }
`;

const fallRain = keyframes`
  0% { transform: translateY(-100px); }
  100% { transform: translateY(calc(100vh + 100px)); }
`;

const fallSnow = keyframes`
  0% {
    transform: translateY(-10px) translateX(0);
  }
  25% {
    transform: translateY(calc(25vh)) translateX(10px);
  }
  50% {
    transform: translateY(calc(50vh)) translateX(-10px);
  }
  75% {
    transform: translateY(calc(75vh)) translateX(10px);
  }
  100% {
    transform: translateY(calc(100vh + 10px)) translateX(0);
  }
`;

const lightning = keyframes`
  0%, 95%, 100% {
    opacity: 0;
  }
  96%, 99% {
    opacity: 1;
  }
`;

const shake = keyframes`
  0%, 100% {
    transform: translateX(0);
  }
  10%, 30%, 50%, 70%, 90% {
    transform: translateX(-5px);
  }
  20%, 40%, 60%, 80% {
    transform: translateX(5px);
  }
`;

// Create styled components for different weather backgrounds
const BackgroundContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: -1;
  transition: background 1s ease-in-out;
  animation: ${fadeIn} 1s ease-in-out;
  overflow: hidden;
  background: var(--background-primary);

  @media (prefers-color-scheme: dark) {
    background: var(--background-dark);
  }
`;

// Weather Effects Container - for animated elements
const WeatherEffectsContainer = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  pointer-events: none;
  z-index: 1;
`;

const RainBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #1a2a56, #0c1c3d);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1501999635878-71cb5379c2d8?q=80&w=1469&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.3) blur(3px);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #0c1c3d, #050d1f);
  }
`;

const ThunderstormBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #0f1a33, #060d1a);
  animation: ${props => props.flash ? `${shake} 0.5s` : 'none'};

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1605727216801-e27ce1d0cc28?q=80&w=1471&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.2) contrast(1.2);
    opacity: 0.3;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #060d1a, #030610);
  }
`;

const SunnyBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #5E82F4, #8AA3FF);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1566159199990-ac81a2e2ca8c?q=80&w=1470&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.7) saturate(1.2);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #1a2a56, #0c1c3d);
  }
`;

const HeatwaveBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #FF8C66, #FFBD91);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1561647784-2f9c43b07a0b?q=80&w=1470&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.6) saturate(1.5) sepia(0.3);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #7c1e05, #4a2511);
  }
`;

const CloudyBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #6F6F9A, #9999BB);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1534088568595-a066f410bcda?q=80&w=1551&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.5) saturate(0.8);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #2c3e50, #1c2e40);
  }
`;

const FoggyBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #9999BB, #BBBBDD);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1487621167305-5d248087c724?q=80&w=1632&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.6) contrast(0.9) saturate(0.7);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #4b6584, #2c3e50);
  }
`;

const SnowBackground = styled(BackgroundContainer)`
  background: linear-gradient(to bottom, #BBBBDD, #DDDDEE);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1491002052546-bf38f186af56?q=80&w=1508&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.4) saturate(0.8);
    opacity: 0.2;
  }

  @media (prefers-color-scheme: dark) {
    background: linear-gradient(to bottom, #1a2a56, #0c1c3d);
  }
`;

// Enhanced Animated elements with more realistic effects
const RainDrop = styled.div`
  position: absolute;
  width: 2px;
  height: ${props => props.height || 20}px;
  background: linear-gradient(to bottom,
    rgba(173, 216, 230, 0.1),
    rgba(173, 216, 230, 0.8),
    rgba(173, 216, 230, 0.9)
  );
  border-radius: 50% 50% 50% 50% / 60% 60% 40% 40%;
  animation: ${fallRain} ${props => props.duration || '1.5s'} linear infinite;
  animation-delay: ${props => props.delay || '0s'};
  opacity: ${props => props.opacity || 0.7};
  transform: rotate(${props => props.angle || 0}deg);
`;

const HeavyRainDrop = styled(RainDrop)`
  width: 3px;
  height: ${props => props.height || 30}px;
  background: linear-gradient(to bottom,
    rgba(100, 149, 237, 0.2),
    rgba(100, 149, 237, 0.9),
    rgba(100, 149, 237, 1)
  );
  animation: ${fallRain} ${props => props.duration || '1s'} linear infinite;
`;

const Snowflake = styled.div`
  position: absolute;
  width: ${props => props.size || 6}px;
  height: ${props => props.size || 6}px;
  background: white;
  border-radius: 50%;
  opacity: ${props => props.opacity || 0.8};
  animation: ${fallSnow} ${props => props.duration || '10s'} linear infinite;
  animation-delay: ${props => props.delay || '0s'};

  &::before {
    content: '';
    position: absolute;
    top: -2px;
    left: -2px;
    right: -2px;
    bottom: -2px;
    background: radial-gradient(circle, rgba(255, 255, 255, 0.3), transparent);
    border-radius: 50%;
  }
`;

const AnimatedCloud = styled(motion.div)`
  position: absolute;
  background: rgba(255, 255, 255, 0.15);
  border-radius: 50px;
  filter: blur(2px);

  &::before {
    content: '';
    position: absolute;
    top: -10px;
    left: 10px;
    width: 60%;
    height: 80%;
    background: rgba(255, 255, 255, 0.1);
    border-radius: 50px;
    filter: blur(1px);
  }

  &::after {
    content: '';
    position: absolute;
    top: -5px;
    right: 15px;
    width: 40%;
    height: 60%;
    background: rgba(255, 255, 255, 0.08);
    border-radius: 50px;
    filter: blur(1px);
  }
`;

const Sun = styled(motion.div)`
  position: absolute;
  width: 100px;
  height: 100px;
  border-radius: 50%;
  background: radial-gradient(circle at 30% 30%,
    rgba(255, 235, 59, 0.9),
    rgba(255, 193, 7, 0.8),
    rgba(255, 152, 0, 0.6)
  );
  box-shadow:
    0 0 20px rgba(255, 235, 59, 0.4),
    0 0 40px rgba(255, 193, 7, 0.3),
    0 0 60px rgba(255, 152, 0, 0.2);
  top: 10%;
  right: 10%;

  &::before {
    content: '';
    position: absolute;
    top: -20px;
    left: -20px;
    right: -20px;
    bottom: -20px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 235, 59, 0.1), transparent);
  }
`;

// Lightning effects
const LightningBolt = styled.div`
  position: absolute;
  width: 4px;
  height: ${props => props.height || '200px'};
  background: linear-gradient(to bottom,
    rgba(255, 255, 255, 1),
    rgba(173, 216, 230, 0.8),
    rgba(255, 255, 255, 0.9)
  );
  box-shadow:
    0 0 10px rgba(255, 255, 255, 0.8),
    0 0 20px rgba(173, 216, 230, 0.6),
    0 0 30px rgba(255, 255, 255, 0.4);
  opacity: 0;
  animation: ${lightning} ${props => props.duration || '3s'} infinite;
  animation-delay: ${props => props.delay || '0s'};
  transform: rotate(${props => props.rotation || '0deg'}) skew(${props => props.skew || '0deg'});
  transform-origin: top;
  border-radius: 2px;

  &::before {
    content: '';
    position: absolute;
    top: 60%;
    left: -2px;
    width: 8px;
    height: 40%;
    background: inherit;
    transform: rotate(-25deg);
    transform-origin: top;
    border-radius: 2px;
  }
`;

const LightningFlash = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: rgba(255, 255, 255, 0.8);
  opacity: 0;
  animation: ${lightning} ${props => props.duration || '7s'} infinite;
  animation-delay: ${props => props.delay || '0s'};
  pointer-events: none;
  z-index: 1;
`;

// Removed duplicate LightningBolt component

const FogLayer = styled.div`
  position: absolute;
  width: 200%;
  height: 100%;
  background: linear-gradient(
    to right,
    rgba(255, 255, 255, 0) 0%,
    rgba(255, 255, 255, 0.2) 25%,
    rgba(255, 255, 255, 0.2) 50%,
    rgba(255, 255, 255, 0) 75%,
    rgba(255, 255, 255, 0) 100%
  );
  left: -50%;
  animation: ${drift} ${props => props.duration || '30s'} linear infinite;
  animation-delay: ${props => props.delay || '0s'};
  opacity: ${props => props.opacity || 0.5};
  filter: blur(8px);
`;

const HeatDistortion = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  top: 0;
  left: 0;
  background: transparent;
  animation: ${heatDistortion} 8s ease-in-out infinite;
  pointer-events: none;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(
      to bottom,
      rgba(255, 100, 50, 0) 30%,
      rgba(255, 100, 50, 0.1) 50%,
      rgba(255, 100, 50, 0) 70%
    );
    filter: blur(10px);
  }
`;

const WeatherBackground = ({ weatherCondition, intensity = 'medium' }) => {
  // Convert to lowercase for case-insensitive matching
  const condition = weatherCondition?.toLowerCase() || '';

  // Generate animated elements based on weather condition
  const renderAnimatedElements = () => {
    if (condition.includes('thunderstorm')) {
      const rainCount = intensity === 'heavy' ? 200 : intensity === 'light' ? 100 : 150;

      return (
        <WeatherEffectsContainer>
          {/* Heavy rain drops for thunderstorm */}
          {Array.from({ length: rainCount }).map((_, i) => (
            <HeavyRainDrop
              key={`heavy-rain-${i}`}
              delay={`${Math.random() * 2}s`}
              duration={`${Math.random() * 0.5 + 0.8}s`}
              height={Math.random() * 40 + 25}
              opacity={Math.random() * 0.6 + 0.4}
              angle={Math.random() * 10 - 5}
              style={{
                left: `${Math.random() * 110 - 5}%`,
                top: `-50px`,
              }}
            />
          ))}

          {/* Lightning flashes */}
          <LightningFlash duration="6s" delay="1s" />
          <LightningFlash duration="8s" delay="4s" />
          <LightningFlash duration="10s" delay="7s" />

          {/* Lightning bolts */}
          {Array.from({ length: 3 }).map((_, i) => (
            <LightningBolt
              key={`lightning-${i}`}
              height={`${Math.random() * 200 + 150}px`}
              duration={`${Math.random() * 2 + 6}s`}
              delay={`${Math.random() * 8}s`}
              rotation={`${Math.random() * 20 - 10}deg`}
              skew={`${Math.random() * 10 - 5}deg`}
              style={{
                top: `${Math.random() * 20}%`,
                left: `${Math.random() * 80 + 10}%`,
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    if (condition.includes('rain') || condition.includes('drizzle') || condition.includes('shower')) {
      const rainCount = intensity === 'heavy' ? 150 : intensity === 'light' ? 60 : 100;
      const RainComponent = intensity === 'heavy' ? HeavyRainDrop : RainDrop;

      return (
        <WeatherEffectsContainer>
          {Array.from({ length: rainCount }).map((_, i) => (
            <RainComponent
              key={`rain-${i}`}
              delay={`${Math.random() * 3}s`}
              duration={`${Math.random() * 0.8 + 1.2}s`}
              height={Math.random() * 25 + 15}
              opacity={Math.random() * 0.5 + 0.3}
              angle={Math.random() * 8 - 4}
              style={{
                left: `${Math.random() * 110 - 5}%`,
                top: `-30px`,
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    if (condition.includes('snow') || condition.includes('sleet') || condition.includes('blizzard')) {
      const snowCount = intensity === 'heavy' ? 100 : intensity === 'light' ? 40 : 70;

      return (
        <WeatherEffectsContainer>
          {Array.from({ length: snowCount }).map((_, i) => (
            <Snowflake
              key={`snow-${i}`}
              delay={`${Math.random() * 15}s`}
              duration={`${Math.random() * 8 + 12}s`}
              size={Math.random() * 6 + 3}
              opacity={Math.random() * 0.6 + 0.4}
              style={{
                left: `${Math.random() * 110 - 5}%`,
                top: `-20px`,
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    if (condition.includes('fog') || condition.includes('mist') || condition.includes('haze')) {
      const fogLayers = intensity === 'heavy' ? 8 : intensity === 'light' ? 3 : 5;

      return (
        <WeatherEffectsContainer>
          {Array.from({ length: fogLayers }).map((_, i) => (
            <FogLayer
              key={`fog-${i}`}
              duration={`${Math.random() * 30 + 25}s`}
              delay={`${Math.random() * 15}s`}
              opacity={Math.random() * 0.4 + 0.2}
              style={{
                top: `${(i * 100) / fogLayers}%`,
                height: `${100 / fogLayers + 20}%`,
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    if (condition.includes('cloud') || condition.includes('overcast')) {
      const cloudCount = intensity === 'heavy' ? 8 : intensity === 'light' ? 4 : 6;

      return (
        <WeatherEffectsContainer>
          {Array.from({ length: cloudCount }).map((_, i) => (
            <AnimatedCloud
              key={`cloud-${i}`}
              initial={{ x: -200, opacity: 0 }}
              animate={{
                x: [0, 50, 0],
                opacity: [0.3, 0.6, 0.3],
                scale: [1, 1.1, 1],
              }}
              transition={{
                duration: Math.random() * 10 + 15,
                repeat: Infinity,
                repeatType: "reverse",
                delay: i * 2,
                ease: "easeInOut"
              }}
              style={{
                width: Math.random() * 150 + 100,
                height: Math.random() * 80 + 40,
                left: `${Math.random() * 70 + 10}%`,
                top: `${Math.random() * 60 + 10}%`,
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    if (condition.includes('clear') || condition.includes('sun')) {
      const isHeatwave = intensity === 'heavy';

      return (
        <WeatherEffectsContainer>
          <Sun
            animate={{
              scale: [1, 1.08, 1],
              opacity: [0.7, 0.9, 0.7],
              rotate: [0, 360]
            }}
            transition={{
              scale: {
                duration: 6,
                repeat: Infinity,
                ease: "easeInOut"
              },
              opacity: {
                duration: 4,
                repeat: Infinity,
                ease: "easeInOut"
              },
              rotate: {
                duration: 120,
                repeat: Infinity,
                ease: "linear"
              }
            }}
          />

          {isHeatwave && <HeatDistortion />}

          {/* Add some floating particles for sunny weather */}
          {Array.from({ length: 15 }).map((_, i) => (
            <motion.div
              key={`sun-particle-${i}`}
              style={{
                position: 'absolute',
                width: '3px',
                height: '3px',
                background: 'rgba(255, 235, 59, 0.6)',
                borderRadius: '50%',
                left: `${Math.random() * 100}%`,
                top: `${Math.random() * 100}%`,
              }}
              animate={{
                y: [0, -20, 0],
                opacity: [0, 1, 0],
                scale: [0.5, 1, 0.5]
              }}
              transition={{
                duration: Math.random() * 4 + 3,
                repeat: Infinity,
                delay: Math.random() * 5,
                ease: "easeInOut"
              }}
            />
          ))}
        </WeatherEffectsContainer>
      );
    }

    // Default to sun
    return (
      <WeatherEffectsContainer>
        <Sun
          animate={{
            scale: [1, 1.05, 1],
            opacity: [0.8, 0.9, 0.8]
          }}
          transition={{
            duration: 5,
            repeat: Infinity,
            ease: "easeInOut"
          }}
        />
      </WeatherEffectsContainer>
    );
  };

  // State for thunderstorm flash effect
  const [isFlashing, setIsFlashing] = React.useState(false);

  // Effect for thunderstorm flash
  React.useEffect(() => {
    if (condition.includes('thunderstorm')) {
      const flashInterval = setInterval(() => {
        setIsFlashing(true);
        setTimeout(() => setIsFlashing(false), 500);
      }, 8000);

      return () => clearInterval(flashInterval);
    }
  }, [condition]);

  // Return the appropriate background based on weather condition
  const getBackground = () => {
    if (condition.includes('thunderstorm')) {
      return <ThunderstormBackground flash={isFlashing}>{renderAnimatedElements()}</ThunderstormBackground>;
    }

    if (condition.includes('rain') || condition.includes('drizzle') || condition.includes('shower')) {
      return <RainBackground>{renderAnimatedElements()}</RainBackground>;
    }

    if (condition.includes('snow') || condition.includes('sleet') || condition.includes('blizzard')) {
      return <SnowBackground>{renderAnimatedElements()}</SnowBackground>;
    }

    if (condition.includes('fog') || condition.includes('mist') || condition.includes('haze')) {
      return <FoggyBackground>{renderAnimatedElements()}</FoggyBackground>;
    }

    if (condition.includes('cloud') || condition.includes('overcast')) {
      return <CloudyBackground>{renderAnimatedElements()}</CloudyBackground>;
    }

    if (condition.includes('clear') || condition.includes('sun')) {
      // Check if it's a heatwave (temperature > 30°C)
      if (intensity === 'heavy') {
        return <HeatwaveBackground>{renderAnimatedElements()}</HeatwaveBackground>;
      }
      return <SunnyBackground>{renderAnimatedElements()}</SunnyBackground>;
    }

    // Default to sunny background
    return <SunnyBackground>{renderAnimatedElements()}</SunnyBackground>;
  };

  return getBackground();
};

export default WeatherBackground;
