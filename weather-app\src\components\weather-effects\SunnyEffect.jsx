import { useEffect, useRef, useState } from 'react';
import styled from 'styled-components';
import { motion } from 'framer-motion';
import sunRaysImage from '../../assets/images/sun-rays.svg';

const SunnyContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(to bottom, #1a2a56, #0c1c3d);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1566159199990-ac81a2e2ca8c?q=80&w=1470&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.7) saturate(1.2);
    opacity: 0.3;
  }
`;

const Sun = styled(motion.div)`
  position: absolute;
  width: 180px;
  height: 180px;
  border-radius: 50%;
  background: radial-gradient(circle, #ffeb3b, #ff9800);
  box-shadow: 0 0 80px #ff9800, 0 0 30px #ffeb3b;
  opacity: 0.8;
  top: 15%;
  right: 15%;

  &::after {
    content: '';
    position: absolute;
    top: -30px;
    left: -30px;
    right: -30px;
    bottom: -30px;
    border-radius: 50%;
    background: radial-gradient(circle, rgba(255, 235, 59, 0.4), transparent 70%);
  }
`;

const LensFlare = styled.div`
  position: absolute;
  width: 100%;
  height: 100%;
  background: radial-gradient(
    ellipse at ${props => props.x}px ${props => props.y}px,
    rgba(255, 255, 255, 0.2) 0%,
    rgba(255, 255, 255, 0) 60%
  );
  pointer-events: none;
`;

const SunRays = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${props => props.sunRaysUrl});
  background-size: cover;
  opacity: 0.2;
  mix-blend-mode: screen;
  transform: scale(1.5);
`;

const CloudCanvas = styled.canvas`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  opacity: 0.7;
`;

const SunnyEffect = () => {
  const canvasRef = useRef(null);
  const cloudsRef = useRef([]);
  const [flarePosition, setFlarePosition] = useState({ x: window.innerWidth * 0.7, y: window.innerHeight * 0.3 });

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let clouds = cloudsRef.current;

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);

    // Initialize clouds
    clouds = [];
    const cloudCount = 8;

    for (let i = 0; i < cloudCount; i++) {
      clouds.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height * 0.5,
        width: Math.random() * 200 + 100,
        height: Math.random() * 80 + 40,
        speed: Math.random() * 0.5 + 0.1,
        opacity: Math.random() * 0.4 + 0.1
      });
    }

    cloudsRef.current = clouds;

    // Draw clouds
    const drawCloud = (x, y, width, height, opacity) => {
      ctx.beginPath();
      ctx.globalAlpha = opacity;
      ctx.fillStyle = '#ffffff';

      // Draw cloud shape
      ctx.arc(x, y + height * 0.5, height * 0.5, 0, Math.PI * 2);
      ctx.arc(x + width * 0.2, y + height * 0.3, height * 0.6, 0, Math.PI * 2);
      ctx.arc(x + width * 0.4, y + height * 0.4, height * 0.4, 0, Math.PI * 2);
      ctx.arc(x + width * 0.6, y + height * 0.5, height * 0.5, 0, Math.PI * 2);
      ctx.arc(x + width * 0.8, y + height * 0.4, height * 0.4, 0, Math.PI * 2);

      ctx.fill();
      ctx.globalAlpha = 1;
    };

    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      for (let i = 0; i < clouds.length; i++) {
        const cloud = clouds[i];
        drawCloud(cloud.x, cloud.y, cloud.width, cloud.height, cloud.opacity);

        cloud.x += cloud.speed;

        // Reset cloud when it goes off screen
        if (cloud.x > canvas.width + cloud.width) {
          cloud.x = -cloud.width;
          cloud.y = Math.random() * canvas.height * 0.5;
        }
      }

      animationFrameId = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      window.removeEventListener('resize', setCanvasDimensions);
      cancelAnimationFrame(animationFrameId);
    };
  }, []);

  // Update lens flare position on mouse move
  useEffect(() => {
    const handleMouseMove = (e) => {
      setFlarePosition({ x: e.clientX, y: e.clientY });
    };

    window.addEventListener('mousemove', handleMouseMove);

    return () => {
      window.removeEventListener('mousemove', handleMouseMove);
    };
  }, []);

  return (
    <SunnyContainer>
      <Sun
        animate={{
          scale: [1, 1.05, 1],
          opacity: [0.8, 0.85, 0.8]
        }}
        transition={{
          duration: 5,
          repeat: Infinity,
          ease: "easeInOut"
        }}
      />
      <SunRays sunRaysUrl={sunRaysImage} />
      <LensFlare x={flarePosition.x} y={flarePosition.y} />
      <CloudCanvas ref={canvasRef} />
    </SunnyContainer>
  );
};

export default SunnyEffect;
