const API_KEY = '617e1130749e58f5637add190ddaf16e';

let map; // Declare map variable in the outer scope

const cityInput = document.getElementById("city-input");
const searchBtn = document.getElementById("search-btn");
const changeCityBtn = document.getElementById("change-city-btn");
const temperatureElement = document.querySelector(".temperature");
const descriptionElement = document.querySelector(".description");
const windElement = document.getElementById("current-wind-speed");
const dateElement = document.querySelector(".date-time");
const weatherIconElement = document.getElementById("weather-icon");

const weatherInfoSection = document.querySelector(".weather-info");
const highlightsSection = document.querySelector(".highlights");
const upcomingWeatherSection = document.querySelector(".upcoming-weather");

// Initialize the sparkline chart
const ctx = document.getElementById('windSpeedChart').getContext('2d');
const windSpeedChart = new Chart(ctx, {
    type: 'line',
    data: {
        labels: [],
        datasets: [{
            label: 'Wind Speed',
            data: [],
            borderColor: 'rgba(255, 255, 255, 0.7)',
            backgroundColor: 'rgba(255, 255, 255, 0.3)',
            fill: true,
            tension: 0.4
        }]
    },
    options: {
        responsive: true,
        scales: {
            y: {
                beginAtZero: true,
                grid: {
                    display: false
                }
            },
            x: {
                grid: {
                    display: false
                }
            }
        },
        plugins: {
            legend: {
                display: false
            }
        }
    }
});

const ctxHumidity = document.getElementById('humidityDial').getContext('2d');
const humidityValue = 60; // Replace with dynamic humidity value

const humidityDial = new Chart(ctxHumidity, {
    type: 'doughnut',
    data: {
        datasets: [{
            data: [humidityValue, 100 - humidityValue],
            backgroundColor: ['#4CAF50', '#e0e0e0'],
            hoverOffset: 4
        }]
    },
    options: {
        cutout: '70%',
        responsive: true,
        plugins: {
            legend: {
                display: false
            },
            tooltip: {
                enabled: false
            }
        }
    }
});

// Update the humidity value dynamically
document.getElementById('humidity-value').innerText = `${humidityValue}%`;

// Initially hide all the relevant sections
weatherInfoSection.style.display = 'none';
highlightsSection.style.display = 'none';
upcomingWeatherSection.style.display = 'none';

// Initialize the map after DOM content is loaded
document.addEventListener('DOMContentLoaded', () => {
    // Initialize the map here
    map = L.map('map').setView([51.5074, -0.1278], 6); // Set the initial view

    // Add OpenWeatherMap tile layer for temperature
    L.tileLayer(`https://tile.openweathermap.org/map/temp_new/{z}/{x}/{y}.png?appid=${API_KEY}`, {
        maxZoom: 18,
        attribution: 'Map data © <a href="https://openweathermap.org/">OpenWeatherMap</a>'
    }).addTo(map);
});

// Function to generate fake wind speed data based on the current wind speed
function generateFakeWindData(currentWindSpeed, days = 7) {
    const fakeData = [];
    const fluctuationRange = 5; // Maximum fluctuation in wind speed
    for (let i = 0; i < days; i++) {
        const fakeWindSpeed = Math.max(0, currentWindSpeed + (Math.random() * fluctuationRange - (fluctuationRange / 2)));
        fakeData.push(fakeWindSpeed);
    }
    return fakeData.reverse(); // Reverse to show latest data on the right
}

// Search button event listener
searchBtn.addEventListener('click', function () {
    const query = cityInput.value;
    if (!query) {
        alert("Please enter a city name.");
        return;
    }

    const UNITS = 'metric';
    const api_call = `https://api.openweathermap.org/data/2.5/weather?q=${query}&APPID=${API_KEY}&units=${UNITS}`;

    fetchWeather(api_call);
});

// Change city button event listener
changeCityBtn.addEventListener('click', function () {
    cityInput.value = '';
    document.querySelector('.search-container').style.display = 'flex';
    weatherInfoSection.style.display = 'none';
    highlightsSection.style.display = 'none';
    upcomingWeatherSection.style.display = 'none';
});

// Fetch weather function
function fetchWeather(url) {
    searchBtn.disabled = true; // Disable the search button
    fetch(url)
        .then(response => {
            if (!response.ok) {
                throw new Error('Network response was not ok');
            }
            return response.json();
        })
        .then((data) => {
            if (data.main) {
                const temperature = data.main.temp;
                const description = data.weather[0].description;
                const wind = data.wind.speed;

                const iconMap = {
                    'clear sky': 'icons/sunny.png',
                    'few clouds': 'icons/cloudy.png',
                    'scattered clouds': 'icons/cloudy.png',
                    'broken clouds': 'icons/cloudy.png',
                    'shower rain': 'icons/rainy.png',
                    'rain': 'icons/rainy.png',
                    'thunderstorm': 'icons/thunderstorm.png',
                    'snow': 'icons/snow.png',
                    'mist': 'icons/mist.png'
                };

                const weatherIcon = iconMap[description] || 'icons/default.png';

                // Update the current time and date
                const now = new Date();
                const hours = String(now.getHours()).padStart(2, '0');
                const minutes = String(now.getMinutes()).padStart(2, '0');
                const daysOfWeek = ["Sunday", "Monday", "Tuesday", "Wednesday", "Thursday", "Friday", "Saturday"];
                const day = daysOfWeek[now.getDay()];
                const date = now.getDate();
                const month = now.toLocaleString('default', { month: 'long' });
                const year = now.getFullYear();
                const currentTime = `${hours}:${minutes}`;
                const fullDate = `${day}, ${month} ${date}, ${year}`;

                // Update the DOM elements with fetched data
                temperatureElement.innerHTML = `${temperature}°C`;
                descriptionElement.innerHTML = description.charAt(0).toUpperCase() + description.slice(1);
                windElement.innerHTML = `${wind} km/h`;
                dateElement.innerHTML = `${fullDate}, ${currentTime}`;
                document.getElementById("current-wind-speed").innerHTML = `${wind} km/h`;
                document.getElementById("current-time").innerHTML = currentTime;

                // Set the local icon
                weatherIconElement.src = weatherIcon;

                // Update the map view to the city's coordinates
                map.setView([data.coord.lat, data.coord.lon], 10);

                // Show the weather info, highlights, and upcoming weather sections
                weatherInfoSection.style.display = 'block';
                highlightsSection.style.display = 'block';
                upcomingWeatherSection.style.display = 'block';
                document.querySelector('.main-content').style.display = 'block';

                // Hide the search input and button
                document.querySelector('.search-container').style.display = 'none';

                // Generate fake wind data for the past 7 days based on the current wind speed
                const fakeWindData = generateFakeWindData(wind);

                // Update the wind speed sparkline chart
                updateWindSpeedChart(fakeWindData);
            } else {
                alert("City not found. Please try again.");
            }
        })
        .catch((error) => {
            console.error('Error fetching weather data:', error);
            alert("Error fetching weather data. Please try again later.");
        })
        .finally(() => {
            searchBtn.disabled = false; // Re-enable the search button
        });
}

// Function to update the wind speed sparkline chart with actual day names
function updateWindSpeedChart(fakeWindData) {
    // Clear previous data
    windSpeedChart.data.labels = [];
    windSpeedChart.data.datasets[0].data = [];

    // Get today's day index (0 = Sunday, 1 = Monday, ..., 6 = Saturday)
    const today = new Date();
    const todayIndex = today.getDay(); // Get current day index

    // Populate the chart with fake wind data and corresponding days
    fakeWindData.forEach((speed, index) => {
        const dayIndex = (todayIndex + index) % 7; // Wrap around to get the next days
        const dayName = ["Sun", "Mon", "Tue", "Wed", "Thurs", "Fri", "Sat"][dayIndex];

        windSpeedChart.data.labels.push(dayName); // Add the day name as the label
        windSpeedChart.data.datasets[0].data.push(speed); // Add speed data
    });

    // Update the chart
    windSpeedChart.update();
}
