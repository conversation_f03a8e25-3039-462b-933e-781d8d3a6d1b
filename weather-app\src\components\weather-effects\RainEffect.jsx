import { useEffect, useRef } from 'react';
import styled from 'styled-components';
import rainGlassImage from '../../assets/images/rain-glass.svg';

const RainContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(to bottom, rgba(21, 27, 46, 0.8), rgba(15, 21, 38, 0.9));

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1501999635878-71cb5379c2d8?q=80&w=1469&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.3) blur(3px);
    opacity: 0.4;
  }
`;

const GlassOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${props => props.glassUrl});
  background-size: cover;
  opacity: 0.4;
`;

const RainCanvas = styled.canvas`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const RainEffect = ({ intensity = 'medium' }) => {
  const canvasRef = useRef(null);
  const raindropsRef = useRef([]);

  // Set rain intensity
  const getIntensityValues = () => {
    switch (intensity) {
      case 'light':
        return { count: 100, speed: 15, size: 2 };
      case 'heavy':
        return { count: 500, speed: 25, size: 3 };
      case 'medium':
      default:
        return { count: 300, speed: 20, size: 2.5 };
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let raindrops = raindropsRef.current;

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);

    // Initialize raindrops
    const { count, speed, size } = getIntensityValues();
    raindrops = [];

    for (let i = 0; i < count; i++) {
      raindrops.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        length: Math.random() * 20 + 10,
        speed: Math.random() * speed + 10,
        thickness: Math.random() * size + 1,
        opacity: Math.random() * 0.55 + 0.15
      });
    }

    raindropsRef.current = raindrops;

    // Draw raindrops
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      ctx.strokeStyle = 'rgba(174, 194, 224, 0.5)';
      ctx.lineWidth = 1;

      for (let i = 0; i < raindrops.length; i++) {
        const drop = raindrops[i];

        ctx.beginPath();
        ctx.moveTo(drop.x, drop.y);
        ctx.lineTo(drop.x + 0.5, drop.y + drop.length);
        ctx.globalAlpha = drop.opacity;
        ctx.lineWidth = drop.thickness;
        ctx.stroke();

        drop.y += drop.speed;

        // Reset raindrop when it goes off screen
        if (drop.y > canvas.height) {
          drop.y = -drop.length;
          drop.x = Math.random() * canvas.width;
        }
      }

      animationFrameId = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      window.removeEventListener('resize', setCanvasDimensions);
      cancelAnimationFrame(animationFrameId);
    };
  }, [intensity]);

  return (
    <RainContainer>
      <GlassOverlay glassUrl={rainGlassImage} />
      <RainCanvas ref={canvasRef} />
    </RainContainer>
  );
};

export default RainEffect;
