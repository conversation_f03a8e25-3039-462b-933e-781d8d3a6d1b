import { useEffect, useState } from 'react';
import { MapContainer as Leaflet<PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from 'react-leaflet';
import { motion } from 'framer-motion';
import { Card, MapContainer } from '../styles/StyledComponents';
import { getMapTileUrl } from '../services/weatherService';
import 'leaflet/dist/leaflet.css';
import L from 'leaflet';

// Custom marker icon
const customIcon = L.icon({
  iconUrl: 'https://cdn-icons-png.flaticon.com/512/9131/9131546.png',
  iconSize: [36, 36],
  iconAnchor: [18, 36],
  popupAnchor: [0, -36],
  shadowUrl: 'https://unpkg.com/leaflet@1.7.1/dist/images/marker-shadow.png',
  shadowSize: [41, 41],
  shadowAnchor: [13, 41]
});

const WeatherMap = ({ data }) => {
  const [mapKey, setMapKey] = useState(Date.now()); // Used to force re-render the map

  useEffect(() => {
    // Force map re-render when data changes
    if (data) {
      setMapKey(Date.now());
    }
  }, [data]);

  if (!data) return null;

  const { coord, name } = data;
  const position = [coord.lat, coord.lon];

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.4 }}
    >
      <Card>
        <h2 style={{
          display: 'flex',
          alignItems: 'center',
          gap: '8px',
          marginBottom: '15px'
        }}>
          <span className="material-symbols-rounded" style={{ color: 'var(--primary-color)' }}>
            map
          </span>
          Weather Map
        </h2>
        <MapContainer>
          <LeafletMap
            key={mapKey}
            center={position}
            zoom={10}
            style={{ height: '100%', width: '100%' }}
            zoomControl={false}
          >
            <TileLayer
              attribution='&copy; <a href="https://www.openstreetmap.org/copyright">OpenStreetMap</a> contributors'
              url="https://{s}.tile.openstreetmap.org/{z}/{x}/{y}.png"
            />
            <TileLayer
              attribution='&copy; <a href="https://openweathermap.org/">OpenWeatherMap</a>'
              url={getMapTileUrl('temp_new')}
              opacity={0.7}
            />
            <Circle
              center={position}
              radius={10000}
              pathOptions={{
                fillColor: '#5E82F4',
                fillOpacity: 0.2,
                color: '#5E82F4',
                weight: 1
              }}
            />
            <Marker position={position} icon={customIcon}>
              <Popup className="custom-popup">
                <div style={{
                  textAlign: 'center',
                  padding: '10px',
                  fontFamily: 'Inter, sans-serif'
                }}>
                  <h3 style={{
                    margin: '0 0 8px 0',
                    color: 'var(--primary-color)',
                    fontWeight: '600'
                  }}>{name}</h3>
                  <p style={{
                    margin: '0',
                    fontSize: '12px',
                    color: '#6F6F9A'
                  }}>
                    Lat: {coord.lat.toFixed(4)}, Lon: {coord.lon.toFixed(4)}
                  </p>
                </div>
              </Popup>
            </Marker>
          </LeafletMap>
        </MapContainer>
        <div style={{
          marginTop: '12px',
          fontSize: '0.9rem',
          color: 'var(--text-secondary)',
          textAlign: 'center',
          display: 'flex',
          alignItems: 'center',
          justifyContent: 'center',
          gap: '6px'
        }}>
          <span className="material-symbols-rounded" style={{ fontSize: '16px' }}>info</span>
          Interactive weather map showing temperature layers
        </div>
      </Card>
    </motion.div>
  );
};

export default WeatherMap;
