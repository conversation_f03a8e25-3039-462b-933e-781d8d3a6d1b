import { motion } from 'framer-motion';
import {
  Card,
  ForecastHeader,
  ForecastDescription,
  ForecastContainer,
  ForecastCard,
  ForecastCardContent,
  ForecastDay,
  ForecastIcon,
  ForecastTemp
} from '../styles/StyledComponents';
import { formatToShortDay } from '../utils/formatters';
import { getWeatherIconUrl } from '../services/weatherService';
import styled from 'styled-components';

const WeatherDetail = styled.div`
  font-size: 0.85rem;
  color: var(--text-secondary);
  margin-top: 5px;
  text-align: center;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

const Forecast = ({ data }) => {
  if (!data || !data.list) return null;

  // Group forecast data by day
  const dailyData = data.list.reduce((acc, item) => {
    const date = new Date(item.dt * 1000).toLocaleDateString();

    if (!acc[date]) {
      acc[date] = {
        date: item.dt,
        temps: [],
        icons: [],
        descriptions: []
      };
    }

    acc[date].temps.push(item.main.temp);
    acc[date].icons.push(item.weather[0].icon);
    acc[date].descriptions.push(item.weather[0].description);

    return acc;
  }, {});

  // Convert to array and get average values
  const forecastData = Object.values(dailyData).map(day => {
    // Get average temperature
    const avgTemp = day.temps.reduce((sum, temp) => sum + temp, 0) / day.temps.length;

    // Get most frequent icon
    const iconCounts = day.icons.reduce((acc, icon) => {
      acc[icon] = (acc[icon] || 0) + 1;
      return acc;
    }, {});

    const mostFrequentIcon = Object.entries(iconCounts)
      .sort((a, b) => b[1] - a[1])[0][0];

    // Get most frequent description
    const descriptionCounts = day.descriptions.reduce((acc, desc) => {
      acc[desc] = (acc[desc] || 0) + 1;
      return acc;
    }, {});

    const mostFrequentDescription = Object.entries(descriptionCounts)
      .sort((a, b) => b[1] - a[1])[0][0];

    return {
      date: day.date,
      temp: avgTemp,
      icon: mostFrequentIcon,
      description: mostFrequentDescription
    };
  });

  // Limit to 5 days
  const limitedForecast = forecastData.slice(0, 5);

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay: 0.3 }}
    >
      <Card>
        <ForecastHeader>5-Day Forecast</ForecastHeader>
        <ForecastDescription>
          Weather forecast for the next 5 days with daily averages
        </ForecastDescription>
        <ForecastContainer>
          {limitedForecast.map((day, index) => (
            <ForecastCard
              key={day.date}
              as={motion.div}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: 0.1 * index }}
              whileHover={{ scale: 1.05, y: -5 }}
            >
              <ForecastCardContent>
                <ForecastDay>{formatToShortDay(day.date * 1000)}</ForecastDay>
                <ForecastIcon
                  src={getWeatherIconUrl(day.icon)}
                  alt={day.description}
                />
                <ForecastTemp>{Math.round(day.temp)}°C</ForecastTemp>
                <WeatherDetail>{day.description}</WeatherDetail>
              </ForecastCardContent>
            </ForecastCard>
          ))}
        </ForecastContainer>
      </Card>
    </motion.div>
  );
};

export default Forecast;
