import { useState, useEffect } from 'react';
import { fetchCurrentWeather, fetchForecast } from '../services/weatherService';

/**
 * Custom hook to manage weather data
 * @param {string} defaultCity - Default city to fetch weather for
 * @returns {Object} - Weather data and functions
 */
const useWeather = (defaultCity = 'London') => {
  const [currentWeather, setCurrentWeather] = useState(null);
  const [forecast, setForecast] = useState(null);
  const [loading, setLoading] = useState(false);
  const [error, setError] = useState(null);
  const [city, setCity] = useState(defaultCity);

  // Fetch weather data when city changes
  useEffect(() => {
    if (!city) return;
    
    const fetchData = async () => {
      setLoading(true);
      setError(null);
      
      try {
        // Fetch current weather and forecast in parallel
        const [currentData, forecastData] = await Promise.all([
          fetchCurrentWeather(city),
          fetchForecast(city)
        ]);
        
        setCurrentWeather(currentData);
        setForecast(forecastData);
      } catch (err) {
        setError(err.message);
        setCurrentWeather(null);
        setForecast(null);
      } finally {
        setLoading(false);
      }
    };
    
    fetchData();
  }, [city]);

  // Function to search for a new city
  const searchCity = (newCity) => {
    setCity(newCity);
  };

  return {
    currentWeather,
    forecast,
    loading,
    error,
    searchCity,
    city
  };
};

export default useWeather;
