* {
    box-sizing: border-box;
}

html, body {
    margin: 0;
    padding: 0;
    font-family: 'Poppins', sans-serif;
    background: linear-gradient(145deg, #1C1C1E, #2C2C2E); /* Dark gradient background */
    color: #FFFFFF;
    height: 100vh;
}

header {
    padding: 20px;
    background-color: rgba(30, 30, 32, 0.9); /* Darker background for header */
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.main-content {
    display: flex;
    flex-direction: column;
    margin: 0 100px;
    padding: 40px;
    flex: 1;
    background: linear-gradient(145deg, #242424, #323232); /* Gradient background */
    border-radius: 12px;
    box-shadow: 0 8px 20px rgba(0, 0, 0, 0.5); /* Slight shadow for depth */
}

.search-container {
    display: flex;
    margin-bottom: 20px;
}

#city-input {
    padding: 10px;
    border: 1px solid #3A3A3C; /* Subtle border */
    background: linear-gradient(145deg, #2A2A2C, #3A3A3C); /* Gradient input */
    color: #FFFFFF;
    border-radius: 5px;
    flex: 1;
    margin-right: 10px;
}

#search-btn {
    padding: 15px;
    border: none;
    background-color: #81A2F8; /* Soft blue */
    color: white;
    border-radius: 5px;
    cursor: pointer;
    transition: background-color 0.3s;
}

#search-btn:hover {
    background-color: #7091E6;
}

.card {
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); /* Gradient background for card */
    border-radius: 12px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.5);
    padding: 20px;
    margin-bottom: 20px;
}

.highlights {
    flex: 1;
    padding: 20px;
    background: linear-gradient(145deg, rgba(255, 255, 255, 0.1), rgba(255, 255, 255, 0.05)); /* Light transparent gradient */
    border-radius: 10px;
    margin-left: 20px;
} 

.upcoming-weather {
    display: flex;
    justify-content: space-between;
    flex-wrap: wrap;
}

.upcoming-day {
    background: linear-gradient(145deg, #444444, #555555); /* Dark gradient background */
    padding: 10px;
    border-radius: 5px;
    text-align: center;
    flex: 1;
    margin-right: 10px;
    margin-bottom: 10px;
}

.upcoming-day:last-child {
    margin-right: 0;
}

.weather-map {
    width: 100%;
    border-radius: 10px;
    overflow: hidden;
}

.weather-container {
    display: flex;
    justify-content: space-between;
    align-items: flex-start;
    margin-bottom: 20px;
}

.weather-info {
    flex: 1;
    max-width: 300px;
    padding: 20px;
    background: linear-gradient(145deg, #2C2C2E, #3A3A3C); /* Gradient background */
    border-radius: 10px;
    text-align: center;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.weather-info:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
}

.weather-icon {
    width: 80px;
    height: auto;
    margin-bottom: 10px;
}

.temperature {
    font-size: 48px;
    font-weight: bold;
    color: #81A2F8;
}

.description, .date-time {
    font-size: 20px;
}

.change-city-btn {
    padding: 10px 15px;
    border: none;
    background-color: #FF5722;
    color: white;
    border-radius: 5px;
    cursor: pointer;
    margin-top: 10px;
    font-size: 16px;
    transition: background-color 0.3s;
}

.change-city-btn:hover {
    background-color: #E64A19;
}

.highlight-row {
    display: flex;
    justify-content: space-between;
    margin-bottom: 15px;
}

.highlight-item {
    flex: 1;
    margin-right: 10px;
}

.highlight-item:last-child {
    margin-right: 0;
}

.highlight-box {
    background: linear-gradient(145deg, rgba(44, 44, 46, 0.7), rgba(44, 44, 46, 0.5)); /* Gradient background for highlights */
    border-radius: 10px;
    padding: 15px;
    display: flex;
    align-items: center;
    text-align: left;
    min-height: 80px;
    overflow: hidden;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.highlight-box:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
}

.highlight-content {
    display: flex;
    flex-direction: column;
    justify-content: center;
    width: 100%;
}

.highlight-icon {
    font-size: 24px;
    color: #81A2F8;
}

.wind-speed-container {
    display: flex;
    align-items: center;
    position: relative;
}

#current-time {
    position: absolute;
    bottom: 10px;
    right: 10px;
    font-size: 14px;
    color: #fff;
    z-index: 1;
    opacity: 1;
}

.wind-gauge {
    width: 50px;
    height: 50px;
    border: 3px solid white;
    border-radius: 50%;
    position: relative;
    margin-right: 10px;
    overflow: hidden;
}

.wind-gauge-fill {
    width: 100%;
    height: 100%;
    background-color: rgba(255, 255, 255, 0.3);
    position: absolute;
    bottom: 0;
    transform-origin: bottom;
    transition: transform 0.3s ease;
}

.chart-container {
    display: flex;
    justify-content: space-between;
    margin-top: 20px;
}

.chart {
    background: linear-gradient(145deg, rgba(44, 44, 46, 0.9), rgba(44, 44, 46, 0.7)); /* Gradient for charts */
    border-radius: 10px;
    padding: 15px;
    width: 48%;
    box-shadow: 0 2px 5px rgba(0, 0, 0, 0.3);
}

.chart canvas {
    width: 100% !important;
    height: auto !important;
}

.humidity-box {
    background: linear-gradient(145deg, rgba(44, 44, 46, 0.8), rgba(44, 44, 46, 0.6)); /* Gradient for humidity box */
    padding: 15px;
    border-radius: 10px;
    box-shadow: 0 2px 8px rgba(0, 0, 0, 0.4);
    text-align: center;
    max-width: 300px;
    margin: auto;
    transition: transform 0.3s ease, box-shadow 0.3s ease;
}

.humidity-box:hover {
    transform: scale(1.05);
    box-shadow: 0 4px 10px rgba(0, 0, 0, 0.5);
}

.humidity-dial {
    position: relative;
    width: 80%;
    height: 80px;
    margin: 0 auto;
}

.humidity-percentage {
    font-size: 20px;
    margin-top: 10px;
    color: #81A2F8;
}

.humidity-graph {
    width: 100%;
    height: 200px;
    margin-top: 20px;
}

/* Media Queries for Responsive Design */
@media (max-width: 768px) {
    .search-container {
        flex-direction: column;
    }

    #city-input {
        margin-right: 0;
        margin-bottom: 10px;
    }

    .main-content {
        margin: 0 20px;
    }

    .upcoming-weather {
        flex-direction: column;
    }
}
