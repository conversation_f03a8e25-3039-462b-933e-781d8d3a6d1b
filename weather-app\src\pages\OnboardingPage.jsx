import { useState } from 'react';
import { useNavigate } from 'react-router-dom';
import styled from 'styled-components';
import { motion, AnimatePresence } from 'framer-motion';
import { Button } from '../styles/StyledComponents';
import weatherIcon1 from '../assets/icons/weather-icon-1.svg';
import weatherIcon2 from '../assets/icons/weather-icon-2.svg';
import weatherIcon3 from '../assets/icons/weather-icon-3.svg';

// Styled components for onboarding
const OnboardingContainer = styled.div`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  min-height: 100vh;
  padding: 20px;
  background: var(--background-darker);
  background-image:
    radial-gradient(circle at 10% 20%, rgba(108, 92, 231, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 90% 80%, rgba(0, 206, 206, 0.2) 0%, transparent 30%),
    radial-gradient(circle at 50% 50%, rgba(253, 121, 168, 0.1) 0%, transparent 50%);
  position: relative;
  overflow: hidden;

  &::before {
    content: '';
    position: absolute;
    top: -50%;
    left: -50%;
    width: 200%;
    height: 200%;
    background: radial-gradient(circle at center, transparent 30%, rgba(0, 0, 0, 0.2) 100%);
    z-index: 0;
  }
`;

const OnboardingCard = styled(motion.div)`
  background: rgba(255, 255, 255, 0.05);
  backdrop-filter: blur(10px);
  border-radius: var(--border-radius-xl);
  padding: 50px 40px;
  max-width: 500px;
  width: 100%;
  text-align: center;
  box-shadow: var(--shadow-lg), 0 0 30px rgba(108, 92, 231, 0.2);
  display: flex;
  flex-direction: column;
  align-items: center;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  z-index: 1;

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    height: 40%;
    background: linear-gradient(to bottom, rgba(255, 255, 255, 0.08), rgba(255, 255, 255, 0));
    border-radius: inherit;
    pointer-events: none;
  }
`;

const OnboardingImage = styled.img`
  width: 220px;
  height: 220px;
  margin-bottom: 30px;
  object-fit: contain;
  filter: drop-shadow(0 10px 15px rgba(108, 92, 231, 0.3));
  animation: float 3s ease-in-out infinite;

  @keyframes float {
    0% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
    100% { transform: translateY(0px); }
  }
`;

const OnboardingTitle = styled.h1`
  font-size: 2.2rem;
  margin-bottom: 15px;
  background: var(--gradient-purple);
  -webkit-background-clip: text;
  -webkit-text-fill-color: transparent;
  font-weight: 700;
`;

const OnboardingText = styled.p`
  font-size: 1.1rem;
  margin-bottom: 30px;
  color: var(--text-secondary);
  line-height: 1.6;
`;

const ProgressDots = styled.div`
  display: flex;
  justify-content: center;
  margin: 30px 0;
`;

const Dot = styled.div`
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background-color: ${props => props.active ? 'var(--primary-color)' : 'rgba(255, 255, 255, 0.2)'};
  margin: 0 8px;
  transition: all 0.3s ease;
  transform: ${props => props.active ? 'scale(1.2)' : 'scale(1)'};
  box-shadow: ${props => props.active ? '0 0 10px rgba(108, 92, 231, 0.5)' : 'none'};
`;

// Onboarding slides data
const slides = [
  {
    id: 1,
    title: 'Real-Time Weather Updates',
    text: 'Get accurate weather information for any location around the world with beautiful visualizations.',
    image: weatherIcon1,
    imageAlt: 'Weather icon showing sun and clouds'
  },
  {
    id: 2,
    title: 'Detailed Forecasts',
    text: 'View detailed forecasts with interactive charts and comprehensive weather data.',
    image: weatherIcon2,
    imageAlt: 'Weather forecast chart'
  },
  {
    id: 3,
    title: 'Weather Maps',
    text: 'Explore interactive weather maps showing temperature, precipitation, and more.',
    image: weatherIcon3,
    imageAlt: 'Weather map'
  }
];

const OnboardingPage = () => {
  const [currentSlide, setCurrentSlide] = useState(0);
  const navigate = useNavigate();

  const handleNext = () => {
    if (currentSlide < slides.length - 1) {
      setCurrentSlide(currentSlide + 1);
    } else {
      // Navigate to main app when onboarding is complete
      navigate('/weather');
    }
  };

  const handleSkip = () => {
    navigate('/weather');
  };

  return (
    <OnboardingContainer>
      <AnimatePresence mode="wait">
        <OnboardingCard
          key={currentSlide}
          initial={{ opacity: 0, x: 100 }}
          animate={{ opacity: 1, x: 0 }}
          exit={{ opacity: 0, x: -100 }}
          transition={{ duration: 0.5 }}
        >
          {/* Placeholder for image - we'll create these later */}
          <OnboardingImage
            src={slides[currentSlide].image}
            alt={slides[currentSlide].imageAlt}
          />

          <OnboardingTitle>{slides[currentSlide].title}</OnboardingTitle>
          <OnboardingText>{slides[currentSlide].text}</OnboardingText>

          <ProgressDots>
            {slides.map((_, index) => (
              <Dot key={index} active={index === currentSlide} />
            ))}
          </ProgressDots>

          <div style={{ display: 'flex', justifyContent: 'space-between', width: '100%' }}>
            <Button
              as={motion.button}
              onClick={handleSkip}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                backgroundColor: 'rgba(255, 255, 255, 0.1)',
                border: '1px solid rgba(255, 255, 255, 0.2)',
                color: 'white',
                backdropFilter: 'blur(5px)',
                padding: '12px 24px',
                borderRadius: '30px',
                fontWeight: '500'
              }}
            >
              Skip
            </Button>

            <Button
              as={motion.button}
              onClick={handleNext}
              whileHover={{ scale: 1.05 }}
              whileTap={{ scale: 0.95 }}
              style={{
                background: 'var(--gradient-purple)',
                boxShadow: '0 5px 15px rgba(108, 92, 231, 0.4)',
                padding: '12px 30px',
                borderRadius: '30px',
                fontWeight: '600',
                fontSize: '1.1rem'
              }}
            >
              {currentSlide < slides.length - 1 ? 'Next' : 'Get Started'}
            </Button>
          </div>
        </OnboardingCard>
      </AnimatePresence>
    </OnboardingContainer>
  );
};

export default OnboardingPage;
