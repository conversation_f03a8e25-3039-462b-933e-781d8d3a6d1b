import { createGlobalStyle } from 'styled-components';

const GlobalStyles = createGlobalStyle`
  :root {
    /* Color variables - Updated to match Dribbble designs */
    --primary-color: #5E82F4; /* Main blue color from reference */
    --primary-light: #8AA3FF;
    --secondary-color: #FF8C66; /* Orange accent */
    --accent-color: #33DDFB; /* Light blue accent */
    --accent-secondary: #FF6584; /* Pink accent */

    /* Gradient colors */
    --gradient-primary: linear-gradient(135deg, #5E82F4, #8AA3FF);
    --gradient-secondary: linear-gradient(135deg, #FF8C66, #FFBD91);
    --gradient-blue: linear-gradient(135deg, #33DDFB, #88EAFF);
    --gradient-purple: linear-gradient(135deg, #9C6FFF, #C1A5FF);
    --gradient-pink: linear-gradient(135deg, #FF6584, #FFA1B5);

    /* Background colors */
    --background-primary: #F6F8FF; /* Light background from reference */
    --background-secondary: #FFFFFF; /* Card background */
    --background-dark: #1A1A2E; /* Dark mode background */
    --background-card-dark: #252547; /* Dark mode card background */
    --background-glass: rgba(255, 255, 255, 0.8); /* Glass effect for cards */
    --background-glass-dark: rgba(37, 37, 71, 0.8); /* Dark glass effect */

    /* Text colors */
    --text-primary: #2C2C54; /* Dark text for light mode */
    --text-secondary: #6F6F9A; /* Secondary text for light mode */
    --text-light: #FFFFFF; /* Light text for dark backgrounds */
    --text-light-secondary: rgba(255, 255, 255, 0.7);

    /* Status colors */
    --success-color: #4CD964;
    --warning-color: #FFCC00;
    --danger-color: #FF3B30;

    /* Spacing variables */
    --spacing-xs: 4px;
    --spacing-sm: 8px;
    --spacing-md: 16px;
    --spacing-lg: 24px;
    --spacing-xl: 32px;
    --spacing-xxl: 48px;

    /* Border radius - Updated for more rounded corners */
    --border-radius-sm: 16px;
    --border-radius-md: 24px;
    --border-radius-lg: 32px;
    --border-radius-xl: 40px;
    --border-radius-circle: 50%;

    /* Shadows - Enhanced for more depth */
    --shadow-sm: 0 4px 12px rgba(94, 130, 244, 0.1);
    --shadow-md: 0 8px 24px rgba(94, 130, 244, 0.15);
    --shadow-lg: 0 16px 32px rgba(94, 130, 244, 0.2);
    --shadow-card: 0 20px 40px rgba(94, 130, 244, 0.12);
    --shadow-glow: 0 0 20px rgba(94, 130, 244, 0.3);

    /* Transitions */
    --transition-fast: 0.2s ease;
    --transition-normal: 0.3s ease;
    --transition-slow: 0.5s ease;

    /* Dark mode toggle - initial state is light mode */
    --is-dark-mode: 0;
  }

  * {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
  }

  html, body {
    font-family: 'Inter', 'Poppins', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, Oxygen,
      Ubuntu, Cantarell, 'Open Sans', 'Helvetica Neue', sans-serif;
    background: var(--background-primary);
    color: var(--text-primary);
    min-height: 100vh;
    overflow-x: hidden;
    transition: background-color var(--transition-normal), color var(--transition-normal);
  }

  /* Dark mode styles */
  @media (prefers-color-scheme: dark) {
    html, body {
      background: var(--background-dark);
      color: var(--text-light);
    }
  }

  /* Apply dark mode when manually toggled */
  html[data-theme='dark'] body {
    background: var(--background-dark);
    color: var(--text-light);
  }

  #root {
    display: flex;
    flex-direction: column;
    min-height: 100vh;
  }

  h1, h2, h3, h4, h5, h6 {
    font-weight: 700;
    line-height: 1.2;
    margin-bottom: var(--spacing-md);
    color: var(--text-primary);
  }

  h1 {
    font-size: 2.5rem;
  }

  h2 {
    font-size: 2rem;
  }

  h3 {
    font-size: 1.5rem;
  }

  p {
    margin-bottom: var(--spacing-md);
    line-height: 1.6;
  }

  a {
    color: var(--primary-color);
    text-decoration: none;
    transition: color var(--transition-fast);

    &:hover {
      color: var(--primary-light);
    }
  }

  button {
    cursor: pointer;
    font-family: inherit;
    border: none;
    background: none;
    font-weight: 600;
    transition: all var(--transition-fast);

    &:disabled {
      cursor: not-allowed;
      opacity: 0.6;
    }
  }

  input, select, textarea {
    font-family: inherit;
    color: inherit;
    border-radius: var(--border-radius-md);
    border: 2px solid rgba(94, 130, 244, 0.1);
    padding: 12px 16px;
    background: var(--background-secondary);
    transition: all var(--transition-fast);

    &:focus {
      outline: none;
      border-color: var(--primary-color);
      box-shadow: 0 0 0 3px rgba(94, 130, 244, 0.2);
    }
  }

  /* Scrollbar styling */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    background: rgba(94, 130, 244, 0.05);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb {
    background: var(--primary-light);
    border-radius: 10px;
  }

  ::-webkit-scrollbar-thumb:hover {
    background: var(--primary-color);
  }

  /* Utility classes */
  .text-gradient {
    background: var(--gradient-primary);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
    text-fill-color: transparent;
  }

  .card {
    background: var(--background-secondary);
    border-radius: var(--border-radius-lg);
    box-shadow: var(--shadow-card);
    padding: var(--spacing-lg);
    transition: transform var(--transition-normal), box-shadow var(--transition-normal);

    &:hover {
      transform: translateY(-5px);
      box-shadow: var(--shadow-lg);
    }
  }

  .flex {
    display: flex;
  }

  .flex-col {
    flex-direction: column;
  }

  .items-center {
    align-items: center;
  }

  .justify-center {
    justify-content: center;
  }

  .justify-between {
    justify-content: space-between;
  }

  .gap-sm {
    gap: var(--spacing-sm);
  }

  .gap-md {
    gap: var(--spacing-md);
  }

  .gap-lg {
    gap: var(--spacing-lg);
  }
`;

export default GlobalStyles;
