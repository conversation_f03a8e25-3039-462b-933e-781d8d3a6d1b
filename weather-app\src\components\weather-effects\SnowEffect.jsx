import { useEffect, useRef } from 'react';
import styled from 'styled-components';
import frostOverlayImage from '../../assets/images/frost-overlay.svg';

const SnowContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(to bottom, #1a2a56, #0c1c3d);

  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1491002052546-bf38f186af56?q=80&w=1508&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.4) saturate(0.8);
    opacity: 0.4;
  }
`;

const SnowCanvas = styled.canvas`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const FrostOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background-image: url(${props => props.frostUrl});
  background-size: cover;
  opacity: 0.15;
  mix-blend-mode: screen;
`;

const SnowEffect = ({ intensity = 'medium' }) => {
  const canvasRef = useRef(null);
  const snowflakesRef = useRef([]);

  // Set snow intensity
  const getIntensityValues = () => {
    switch (intensity) {
      case 'light':
        return { count: 100, speed: 1, size: 3 };
      case 'heavy':
        return { count: 400, speed: 2, size: 5 };
      case 'medium':
      default:
        return { count: 200, speed: 1.5, size: 4 };
    }
  };

  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let snowflakes = snowflakesRef.current;

    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };

    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);

    // Initialize snowflakes
    const { count, speed, size } = getIntensityValues();
    snowflakes = [];

    for (let i = 0; i < count; i++) {
      snowflakes.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height,
        radius: Math.random() * size + 1,
        speed: Math.random() * speed + 0.5,
        wind: Math.random() * 0.5 - 0.25,
        opacity: Math.random() * 0.5 + 0.5
      });
    }

    snowflakesRef.current = snowflakes;

    // Draw snowflakes
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);

      for (let i = 0; i < snowflakes.length; i++) {
        const flake = snowflakes[i];

        ctx.beginPath();
        ctx.arc(flake.x, flake.y, flake.radius, 0, Math.PI * 2);
        ctx.fillStyle = `rgba(255, 255, 255, ${flake.opacity})`;
        ctx.fill();

        // Update snowflake position
        flake.y += flake.speed;
        flake.x += flake.wind;

        // Add some wobble
        flake.x += Math.sin(flake.y / 30) * 0.3;

        // Reset snowflake when it goes off screen
        if (flake.y > canvas.height) {
          flake.y = -10;
          flake.x = Math.random() * canvas.width;
        }

        if (flake.x > canvas.width) {
          flake.x = 0;
        } else if (flake.x < 0) {
          flake.x = canvas.width;
        }
      }

      animationFrameId = requestAnimationFrame(draw);
    };

    draw();

    return () => {
      window.removeEventListener('resize', setCanvasDimensions);
      cancelAnimationFrame(animationFrameId);
    };
  }, [intensity]);

  return (
    <SnowContainer>
      <SnowCanvas ref={canvasRef} />
      <FrostOverlay frostUrl={frostOverlayImage} />
    </SnowContainer>
  );
};

export default SnowEffect;
