import { useEffect, useRef } from 'react';
import styled from 'styled-components';

const CloudyContainer = styled.div`
  position: fixed;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  pointer-events: none;
  z-index: -1;
  overflow: hidden;
  background: linear-gradient(to bottom, #2c3e50, #1c2e40);
  
  &::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background-image: url('https://images.unsplash.com/photo-1534088568595-a066f410bcda?q=80&w=1551&auto=format&fit=crop');
    background-size: cover;
    background-position: center;
    filter: brightness(0.5) saturate(0.8);
    opacity: 0.3;
  }
`;

const CloudCanvas = styled.canvas`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
`;

const FogOverlay = styled.div`
  position: absolute;
  top: 0;
  left: 0;
  width: 100%;
  height: 100%;
  background: linear-gradient(to bottom, 
    rgba(255, 255, 255, 0.1), 
    rgba(255, 255, 255, 0.05) 30%, 
    rgba(255, 255, 255, 0.02) 60%, 
    rgba(255, 255, 255, 0)
  );
  opacity: 0.5;
`;

const CloudyEffect = ({ intensity = 'medium' }) => {
  const canvasRef = useRef(null);
  const cloudsRef = useRef([]);
  
  // Set cloud intensity
  const getIntensityValues = () => {
    switch (intensity) {
      case 'light':
        return { count: 8, opacity: 0.5, speed: 0.2 };
      case 'heavy':
        return { count: 20, opacity: 0.8, speed: 0.4 };
      case 'medium':
      default:
        return { count: 12, opacity: 0.6, speed: 0.3 };
    }
  };
  
  useEffect(() => {
    const canvas = canvasRef.current;
    const ctx = canvas.getContext('2d');
    let animationFrameId;
    let clouds = cloudsRef.current;
    
    // Set canvas dimensions
    const setCanvasDimensions = () => {
      canvas.width = window.innerWidth;
      canvas.height = window.innerHeight;
    };
    
    setCanvasDimensions();
    window.addEventListener('resize', setCanvasDimensions);
    
    // Initialize clouds
    const { count, opacity, speed } = getIntensityValues();
    clouds = [];
    
    for (let i = 0; i < count; i++) {
      clouds.push({
        x: Math.random() * canvas.width,
        y: Math.random() * canvas.height * 0.6,
        width: Math.random() * 300 + 200,
        height: Math.random() * 100 + 60,
        speed: Math.random() * speed + 0.1,
        opacity: Math.random() * 0.3 + opacity * 0.5
      });
    }
    
    cloudsRef.current = clouds;
    
    // Draw clouds
    const drawCloud = (x, y, width, height, opacity) => {
      ctx.beginPath();
      ctx.globalAlpha = opacity;
      
      // Create gradient for cloud
      const gradient = ctx.createLinearGradient(x, y, x, y + height);
      gradient.addColorStop(0, 'rgba(255, 255, 255, 0.8)');
      gradient.addColorStop(1, 'rgba(230, 240, 255, 0.6)');
      ctx.fillStyle = gradient;
      
      // Draw cloud shape
      ctx.arc(x, y + height * 0.5, height * 0.5, 0, Math.PI * 2);
      ctx.arc(x + width * 0.2, y + height * 0.3, height * 0.6, 0, Math.PI * 2);
      ctx.arc(x + width * 0.4, y + height * 0.4, height * 0.7, 0, Math.PI * 2);
      ctx.arc(x + width * 0.6, y + height * 0.5, height * 0.6, 0, Math.PI * 2);
      ctx.arc(x + width * 0.8, y + height * 0.4, height * 0.5, 0, Math.PI * 2);
      ctx.arc(x + width, y + height * 0.5, height * 0.4, 0, Math.PI * 2);
      
      ctx.fill();
      ctx.globalAlpha = 1;
    };
    
    const draw = () => {
      ctx.clearRect(0, 0, canvas.width, canvas.height);
      
      for (let i = 0; i < clouds.length; i++) {
        const cloud = clouds[i];
        drawCloud(cloud.x, cloud.y, cloud.width, cloud.height, cloud.opacity);
        
        cloud.x += cloud.speed;
        
        // Reset cloud when it goes off screen
        if (cloud.x > canvas.width + cloud.width) {
          cloud.x = -cloud.width;
          cloud.y = Math.random() * canvas.height * 0.6;
        }
      }
      
      animationFrameId = requestAnimationFrame(draw);
    };
    
    draw();
    
    return () => {
      window.removeEventListener('resize', setCanvasDimensions);
      cancelAnimationFrame(animationFrameId);
    };
  }, [intensity]);
  
  return (
    <CloudyContainer>
      <CloudCanvas ref={canvasRef} />
      <FogOverlay />
    </CloudyContainer>
  );
};

export default CloudyEffect;
