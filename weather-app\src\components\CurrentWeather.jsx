import { motion } from 'framer-motion';
import {
  CurrentWeatherCard,
  LocationBadge,
  WeatherIcon,
  Temperature,
  WeatherDescription,
  DateTime,
  WeatherDetails,
  WeatherDetail
} from '../styles/StyledComponents';
import { formatDateTime, capitalizeWords } from '../utils/formatters';
import { getWeatherIconUrl } from '../services/weatherService';
import styled from 'styled-components';

const MinMaxTemp = styled.div`
  display: flex;
  justify-content: center;
  gap: 20px;
  margin: 15px 0;
  font-size: 1.1rem;
  color: var(--text-secondary);
  z-index: 1;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

const CurrentWeather = ({ data }) => {
  if (!data) return null;

  const {
    weather,
    main,
    dt,
    name,
    sys,
    wind,
    visibility
  } = data;

  return (
    <CurrentWeatherCard
      as={motion.div}
      initial={{ opacity: 0, scale: 0.9 }}
      animate={{ opacity: 1, scale: 1 }}
      transition={{ duration: 0.5 }}
    >
      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.1, duration: 0.5 }}
      >
        <LocationBadge>
          <span className="material-symbols-rounded">location_on</span>
          {name}, {sys.country}
        </LocationBadge>
      </motion.div>

      <motion.div
        initial={{ y: -20, opacity: 0 }}
        animate={{ y: 0, opacity: 1 }}
        transition={{ delay: 0.2, duration: 0.5 }}
      >
        <WeatherIcon
          src={getWeatherIconUrl(weather[0].icon)}
          alt={weather[0].description}
        />
      </motion.div>

      <motion.div
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ delay: 0.3, duration: 0.5 }}
      >
        <Temperature>{Math.round(main.temp)}°C</Temperature>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.4, duration: 0.5 }}
      >
        <WeatherDescription>
          {capitalizeWords(weather[0].description)}
        </WeatherDescription>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 10 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.5, duration: 0.5 }}
      >
        <MinMaxTemp>
          <span>Min: {Math.round(main.temp_min)}°C</span>
          <span>Max: {Math.round(main.temp_max)}°C</span>
        </MinMaxTemp>
      </motion.div>

      <motion.div
        initial={{ opacity: 0 }}
        animate={{ opacity: 1 }}
        transition={{ delay: 0.6, duration: 0.5 }}
      >
        <DateTime>{formatDateTime(dt)}</DateTime>
      </motion.div>

      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: 0.7, duration: 0.5 }}
      >
        <WeatherDetails>
          <WeatherDetail>
            <span>
              <span className="material-symbols-rounded">thermostat</span>
              Feels Like
            </span>
            <span>{Math.round(main.feels_like)}°C</span>
          </WeatherDetail>
          <WeatherDetail>
            <span>
              <span className="material-symbols-rounded">humidity_percentage</span>
              Humidity
            </span>
            <span>{main.humidity}%</span>
          </WeatherDetail>
          <WeatherDetail>
            <span>
              <span className="material-symbols-rounded">air</span>
              Wind
            </span>
            <span>{wind.speed} km/h</span>
          </WeatherDetail>
          <WeatherDetail>
            <span>
              <span className="material-symbols-rounded">visibility</span>
              Visibility
            </span>
            <span>{(visibility / 1000).toFixed(1)} km</span>
          </WeatherDetail>
        </WeatherDetails>
      </motion.div>
    </CurrentWeatherCard>
  );
};

export default CurrentWeather;
