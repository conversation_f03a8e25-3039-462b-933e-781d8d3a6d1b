body {
  background-color: #1e1e1e;
  font-family: <PERSON>l, sans-serif;
}

.wind-status-box {
  background: linear-gradient(145deg, #1e1e1e, #2d2d2d);
  border-radius: 15px;
  padding: 20px;
  color: #fff;
  width: 320px;
  margin: 50px auto;
  box-shadow: 0 8px 20px rgba(0, 0, 0, 0.2);
}

.wind-status-box h2 {
  font-size: 18px;
  text-align: left;
  margin-bottom: 10px;
}

canvas {
  display: block;
  margin: 0 auto;
  background-color: transparent;
}

.info {
  display: flex;
  justify-content: space-between;
  margin-top: 15px;
}

.wind-speed {
  font-size: 24px;
  color: #fff;
}

.time {
  font-size: 14px;
  color: #999;
}
