/**
 * Format temperature with degree symbol
 * @param {number} temp - Temperature value
 * @param {boolean} showUnit - Whether to show unit (°C)
 * @returns {string} - Formatted temperature
 */
export const formatTemperature = (temp, showUnit = true) => {
  const roundedTemp = Math.round(temp);
  return showUnit ? `${roundedTemp}°C` : `${roundedTemp}°`;
};

/**
 * Format date to day name
 * @param {string} dateStr - Date string
 * @returns {string} - Day name (e.g., "Monday")
 */
export const formatToDay = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', { weekday: 'long' });
};

/**
 * Format date to short day name
 * @param {string} dateStr - Date string
 * @returns {string} - Short day name (e.g., "Mon")
 */
export const formatToShortDay = (dateStr) => {
  const date = new Date(dateStr);
  return date.toLocaleDateString('en-US', { weekday: 'short' });
};

/**
 * Format time from timestamp
 * @param {number} timestamp - Unix timestamp
 * @returns {string} - Formatted time (e.g., "14:30")
 */
export const formatTime = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleTimeString('en-US', { hour: '2-digit', minute: '2-digit', hour12: true });
};

/**
 * Format date and time
 * @param {number} timestamp - Unix timestamp
 * @returns {string} - Formatted date and time
 */
export const formatDateTime = (timestamp) => {
  const date = new Date(timestamp * 1000);
  return date.toLocaleDateString('en-US', {
    weekday: 'long',
    month: 'long',
    day: 'numeric',
    hour: '2-digit',
    minute: '2-digit',
    hour12: true
  });
};

/**
 * Capitalize first letter of each word
 * @param {string} str - String to capitalize
 * @returns {string} - Capitalized string
 */
export const capitalizeWords = (str) => {
  return str
    .split(' ')
    .map(word => word.charAt(0).toUpperCase() + word.slice(1))
    .join(' ');
};
