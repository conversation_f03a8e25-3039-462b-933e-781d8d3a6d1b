// script.js
const canvas = document.getElementById('uvGauge');
const ctx = canvas.getContext('2d');
const uvValueElement = document.getElementById('uvValue');

function drawGauge(value) {
    // Clear the canvas
    ctx.clearRect(0, 0, canvas.width, canvas.height);

    // Draw background arc (outer grey arc)
    ctx.beginPath();
    ctx.arc(150, 150, 100, Math.PI, 0); // Full arc
    ctx.lineWidth = 20;
    ctx.strokeStyle = '#D9D9D9'; // Light grey for the background
    ctx.stroke();

    // Draw middle white arc
    ctx.beginPath();
    ctx.arc(150, 150, 90, Math.PI, 0); // Slightly smaller arc for white line
    ctx.lineWidth = 10; // Thin white line
    ctx.strokeStyle = 'white'; // White for the middle line
    ctx.stroke();

    // Create a gradient for the current value arc
    const gradient = ctx.createLinearGradient(0, 0, 0, canvas.height);
    gradient.addColorStop(0, '#4a90e2'); // Starting color (blue)
    gradient.addColorStop(1, '#50e3c2'); // Ending color (light teal)

    // Draw current value arc (blue gradient)
    const endAngle = Math.PI + (Math.PI * (value / 10)); // Adjust for UV index scale
    ctx.beginPath();
    ctx.arc(150, 150, 100, Math.PI, endAngle);
    ctx.lineWidth = 20; // Arc width
    ctx.strokeStyle = gradient; // Use the gradient for the arc
    ctx.stroke();

    // Display UV index value
    uvValueElement.innerText = value;
}

// Simulating UV index data (replace this with real data fetching logic)
function updateUVIndex() {
    const randomUVValue = Math.floor(Math.random() * 11); // Simulated value between 0 and 10
    drawGauge(randomUVValue);
}

// Update the gauge every second
setInterval(updateUVIndex, 1000);
