import { useState } from 'react';
import { SearchContainer, SearchInput, SearchIcon, Button } from '../styles/StyledComponents';
import { motion } from 'framer-motion';
import styled from 'styled-components';

const SearchForm = styled(motion.form)`
  width: 100%;
  max-width: 600px;
  margin: 0 auto;
`;

const SearchTitle = styled.h1`
  text-align: center;
  margin-bottom: 20px;
  font-size: 2.5rem;
  font-weight: 800;
  color: var(--text-primary);

  span {
    color: var(--primary-color);
  }

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

const SearchSubtitle = styled.p`
  text-align: center;
  margin-bottom: 30px;
  color: var(--text-secondary);
  font-size: 1.1rem;
  max-width: 500px;
  margin-left: auto;
  margin-right: auto;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

const StyledButton = styled(Button)`
  font-size: 1.1rem;
`;

const SearchBar = ({ onSearch, initialValue = '' }) => {
  const [city, setCity] = useState(initialValue);

  const handleSubmit = (e) => {
    e.preventDefault();
    if (city.trim()) {
      onSearch(city.trim());
    }
  };

  return (
    <SearchForm
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      onSubmit={handleSubmit}
    >
      <SearchTitle>
        Weather <span>Forecast</span>
      </SearchTitle>
      <SearchSubtitle>
        Enter a city name to get the current weather and forecast
      </SearchSubtitle>

      <SearchContainer>
        <SearchIcon>
          <span className="material-symbols-rounded">search</span>
        </SearchIcon>
        <SearchInput
          type="text"
          value={city}
          onChange={(e) => setCity(e.target.value)}
          placeholder="Enter city name..."
          aria-label="City name"
        />
        <StyledButton
          type="submit"
          primary
          as={motion.button}
          whileHover={{ scale: 1.05 }}
          whileTap={{ scale: 0.95 }}
        >
          <span className="material-symbols-rounded">travel_explore</span>
          Search
        </StyledButton>
      </SearchContainer>
    </SearchForm>
  );
};

export default SearchBar;
