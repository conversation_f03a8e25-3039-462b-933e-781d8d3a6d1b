import styled, { keyframes, css } from 'styled-components';
import { LoadingContainer, LoadingText as StyledLoadingText } from '../styles/StyledComponents';
import { motion } from 'framer-motion';

const spin = keyframes`
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
`;

const pulse = keyframes`
  0% { transform: scale(0.95); opacity: 0.7; }
  50% { transform: scale(1.05); opacity: 1; }
  100% { transform: scale(0.95); opacity: 0.7; }
`;

const shimmer = keyframes`
  0% { background-position: -200% 0; }
  100% { background-position: 200% 0; }
`;

const spinAnimation = css`
  ${spin} 1.5s linear infinite
`;

const LoadingWrapper = styled(motion.div)`
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  padding: 40px;
  background: var(--background-secondary);
  border-radius: var(--border-radius-lg);
  box-shadow: var(--shadow-card);

  @media (prefers-color-scheme: dark) {
    background: var(--background-card-dark);
  }
`;

const LoadingSpinner = styled.div`
  width: 60px;
  height: 60px;
  border: 3px solid rgba(94, 130, 244, 0.1);
  border-radius: 50%;
  border-top-color: var(--primary-color);
  border-left-color: var(--accent-color);
  border-right-color: var(--secondary-color);
  animation: ${spin} 1.2s ease-in-out infinite;
  box-shadow: 0 0 15px rgba(94, 130, 244, 0.2);
`;

const LoadingText = styled.div`
  margin-top: 24px;
  font-size: 1.3rem;
  font-weight: 600;
  color: var(--text-primary);
  animation: ${pulse} 2s ease-in-out infinite;

  @media (prefers-color-scheme: dark) {
    color: var(--text-light);
  }
`;

const LoadingSubtext = styled.div`
  margin-top: 8px;
  font-size: 1rem;
  color: var(--text-secondary);

  @media (prefers-color-scheme: dark) {
    color: var(--text-light-secondary);
  }
`;

const LoadingIcon = styled.span`
  font-size: 40px;
  color: var(--primary-color);
  animation: ${spinAnimation};
`;

const Loading = ({ message = 'Loading weather data...', subtext = 'Please wait while we fetch the latest information' }) => {
  return (
    <LoadingContainer>
      <LoadingWrapper
        initial={{ opacity: 0, scale: 0.9 }}
        animate={{ opacity: 1, scale: 1 }}
        transition={{ duration: 0.5 }}
      >
        <LoadingIcon className="material-symbols-rounded">
          progress_activity
        </LoadingIcon>
        <LoadingText>{message}</LoadingText>
        <LoadingSubtext>{subtext}</LoadingSubtext>
      </LoadingWrapper>
    </LoadingContainer>
  );
};

export default Loading;
