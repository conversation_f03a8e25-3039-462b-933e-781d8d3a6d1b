<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <link rel="stylesheet" href="styles.css">
    <link rel="stylesheet" href="https://unpkg.com/leaflet/dist/leaflet.css">
    <title>Weather App</title>
</head>
<body>
    <header>
        <h1>Weather App</h1>
        <div class="search-container">
            <input type="text" id="city-input" placeholder="Enter city name" aria-label="City Name">
            <button id="search-btn" aria-label="Search">Search</button>
        </div>
    </header>
    <main class="main-content" style="display: none;">
        <section class="weather-container">
            <div class="weather-info">
                <img id="weather-icon" class="weather-icon" src="" alt="Weather Icon">
                <div class="temperature">24°C</div>
                <div class="description">Partly Cloudy</div>
                <div class="date-time">Monday, 2:00 PM</div>
                <button id="change-city-btn" class="change-city-btn">Change City</button>
            </div>
            <aside class="highlights">
                <h2>Today's Highlights</h2>
                <div class="highlight-row">
                    <div class="highlight-item">
                        <div class="highlight-box wind-speed-box">
                            <div class="highlight-content">
                                <h2 class="wind-speed-heading">Wind Speed</h2>
                                <canvas id="windSpeedChart" width="400" height="150"></canvas>
                                <div class="wind-speed-info">
                                    <span id="current-wind-speed">0 km/h</span>
                                    <span id="current-time">12:00</span>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="highlight-item">
                        <div class="humidity-box highlight-box"> <!-- Added highlight-box class -->
                            <h3>Humidity</h3>
                            <div class="humidity-dial">
                                <canvas id="humidityDial" width="150" height="150"></canvas>
                            </div>
                            <div class="humidity-percentage" id="humidity-value">60%</div>
                            <div class="humidity-graph" style="width: 60%;"></div>
                        </div>
                    </div>
                </div>
                <div class="highlight-item feels-like-box"> <!-- Added a container for feels like -->
                    <div class="highlight-box">
                        <span class="highlight-icon" id="feels-like-icon" aria-hidden="true">🌡️</span>
                        <span class="highlight-label">Feels Like:</span>
                        <span id="feels-like">27°C</span>
                    </div>
                </div>
            </aside>
            
        </section>
        <section class="upcoming-weather">
            <h2>Upcoming Weather</h2>
            <div class="upcoming-weather-row">
                <div class="upcoming-day">
                    <div class="day">Mon</div>
                    <div class="temperature">22°C</div>
                    <div class="description">Sunny</div>
                </div>
                <div class="upcoming-day">
                    <div class="day">Tue</div>
                    <div class="temperature">20°C</div>
                    <div class="description">Cloudy</div>
                </div>
                <div class="upcoming-day">
                    <div class="day">Wed</div>
                    <div class="temperature">19°C</div>
                    <div class="description">Rainy</div>
                </div>
            </div>
        </section>

        <section class="weather-map">
            <div id="map" style="height: 500px; width: 100%;"></div>
        </section>
    </main>
    <script src="https://cdn.jsdelivr.net/npm/chart.js"></script>
    <script src="https://unpkg.com/leaflet/dist/leaflet.js"></script>
    <script src="script.js"></script>
</body>
</html>
