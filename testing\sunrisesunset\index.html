<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Sunrise and Sunset Indicator</title>
    <style>
        body {
            background-color: #121212;
            display: flex;
            justify-content: center;
            align-items: center;
            height: 100vh;
            margin: 0;
        }

        .container {
            text-align: center;
            position: relative;
        }

        .sunrise-sunset-box {
            position: relative;
            width: 300px;
            height: 200px;
            margin: 0 auto;
        }

        canvas {
            position: absolute;
            top: 0;
            left: 0;
        }

        h2.sunrise-label{
            color: white;
            margin: 10px 0;
            position: absolute;
            transform: translateY(20px);
            top: 60%;
            left: -25%;
        }

        h2.sunset-label{
            color: white;
            margin: 10px 0;
            position: absolute;
            transform: translateY(20px);
            top: 60%;
            right: -25%;
        }

        .sunrise-label {
            left: 0;  /* Positioned to the left of the arc */
        }

        .sunset-label {
            right: 0;  /* Positioned to the right of the arc */
        }
    </style>
</head>
<body>

<div class="container">
    <div class="sunrise-sunset-box">
        <canvas id="sunriseSunsetCanvas" width="300" height="150"></canvas>
        <h2 class="sunrise-label">Sunrise: 06:00 AM</h2>
        <h2 class="sunset-label">Sunset: 07:00 PM</h2>
    </div>
</div>

<script>
    const canvas = document.getElementById('sunriseSunsetCanvas');
    const ctx = canvas.getContext('2d');

    const sunriseTime = new Date();
    sunriseTime.setHours(6, 0, 0); // 6:00 AM

    const sunsetTime = new Date();
    sunsetTime.setHours(19, 0, 0); // 7:00 PM

    const currentTime = new Date();

    function drawSunArc(sunX, sunY) {
        ctx.clearRect(0, 0, canvas.width, canvas.height);

        // Draw the dashed path arc
        ctx.beginPath();
        ctx.arc(150, 150, 140, Math.PI, 0, false);
        ctx.setLineDash([5, 5]); // Dashed line
        ctx.strokeStyle = '#FFD700';
        ctx.lineWidth = 2;
        ctx.stroke();

        // Draw the sun
        ctx.setLineDash([]); // Reset dash for the sun
        ctx.beginPath();
        ctx.arc(sunX, sunY, 20, 0, Math.PI * 2);
        ctx.fillStyle = '#FFD700';
        ctx.fill();
    }

    // Calculate the percentage of the day that has passed between sunrise and sunset
    const totalDaylightTime = sunsetTime - sunriseTime;
    const timeSinceSunrise = currentTime - sunriseTime;
    let percentageOfDay = timeSinceSunrise / totalDaylightTime;

    // Limit the percentage to the range of 0 to 1
    percentageOfDay = Math.min(Math.max(percentageOfDay, 0), 1);

    // Reverse the percentage to make the sun rise from the right and set on the left
    const angle = Math.PI * percentageOfDay;  // Normal angle movement from left to right

    // Calculate sun's position along the arc based on time
    const sunX = 150 - 140 * Math.cos(angle);   // Left to right along the arc
    const sunY = 150 - 140 * Math.sin(angle);   // Rising upwards and setting downwards

    // Draw the sun at the current position
    drawSunArc(sunX, sunY);
</script>

</body>
</html>
