const API_KEY = '617e1130749e58f5637add190ddaf16e';
const BASE_URL = 'https://api.openweathermap.org/data/2.5';

/**
 * Fetch current weather data for a city
 * @param {string} city - City name
 * @returns {Promise} - Weather data
 */
export const fetchCurrentWeather = async (city) => {
  try {
    const response = await fetch(
      `${BASE_URL}/weather?q=${city}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error('City not found or API error');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching current weather:', error);
    throw error;
  }
};

/**
 * Fetch 5-day forecast data for a city
 * @param {string} city - City name
 * @returns {Promise} - Forecast data
 */
export const fetchForecast = async (city) => {
  try {
    const response = await fetch(
      `${BASE_URL}/forecast?q=${city}&appid=${API_KEY}&units=metric`
    );
    
    if (!response.ok) {
      throw new Error('City not found or API error');
    }
    
    return await response.json();
  } catch (error) {
    console.error('Error fetching forecast:', error);
    throw error;
  }
};

/**
 * Get weather icon URL
 * @param {string} iconCode - Weather icon code
 * @returns {string} - Icon URL
 */
export const getWeatherIconUrl = (iconCode) => {
  return `https://openweathermap.org/img/wn/${iconCode}@2x.png`;
};

/**
 * Get map tile URL for weather layers
 * @param {string} layerType - Type of layer (temp_new, precipitation_new, etc.)
 * @returns {string} - Tile URL template
 */
export const getMapTileUrl = (layerType = 'temp_new') => {
  return `https://tile.openweathermap.org/map/${layerType}/{z}/{x}/{y}.png?appid=${API_KEY}`;
};
